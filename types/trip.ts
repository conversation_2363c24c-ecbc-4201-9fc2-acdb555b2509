export type TripDifficulty = 'easy' | 'moderate' | 'challenging' | 'extreme';

export type TransportMode = 'train' | 'bus' | 'car' | 'flight';

export interface TripItinerary {
  day: number;
  title: string;
  subheading?: string;
  description: string;
  activities?: string[];
  meals?: {
    breakfast?: boolean;
    lunch?: boolean;
    dinner?: boolean;
  };
  accommodation?: string;
  transport_mode?: TransportMode | null;
}

export interface CancellationPolicy {
  days_before: number;
  refund_percentage: number;
  description?: string;
}

export interface Trip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  detailed_description: string | null;
  destination: string;
  days: number;
  nights: number;
  min_age: number | null;
  max_age: number | null;
  price_per_person: number;
  difficulty: TripDifficulty;
  inclusions: string[] | null;
  exclusions: string[] | null;
  itinerary: TripItinerary[] | null;
  featured_image_url: string | null;
  is_trek: boolean | null;
  is_active: boolean | null;
  is_featured: boolean | null;
  created_at: string | null;
  updated_at: string | null;
  category: string | null;
  mode_of_travel: string | null;
  pickup_location: string | null;
  drop_location: string | null;
  property_used: string | null;
  activities: string[] | null;
  optional_activities: string[] | null;
  benefits: string[] | null;
  safety_supervision: string[] | null;
  things_to_carry: string[] | null;
  available_dates: string[] | null; // departure dates in YYYY-MM-DD format
  payment_terms: string | null;
  cancellation_policy: Record<string, string> | null;
  special_notes: string[] | null;
  auto_deactivation_date: string | null; // ISO 8601 datetime string in UTC format
}

export type TripFormData = Omit<Trip, 'id' | 'created_at' | 'updated_at' | 'slug'>;