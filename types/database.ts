// Database types for Positive7 Tourism Website

export type UserRole = 'customer' | 'admin';

export type TripDifficulty = 'easy' | 'moderate' | 'challenging' | 'extreme';
export type InquiryStatus = 'new' | 'in_progress' | 'resolved' | 'closed';

export interface User {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  date_of_birth?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  role: UserRole;
  profile_image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Trip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  detailed_description: string | null;
  destination: string;
  days: number;
  nights: number;
  min_age: number | null;
  max_age: number | null;
  price_per_person: number;
  difficulty: string; // USER-DEFINED enum
  inclusions: string[] | null;
  exclusions: string[] | null;
  itinerary: any; // jsonb - can be object, array, or null
  featured_image_url: string | null;
  is_trek: boolean | null;
  is_active: boolean | null;
  is_featured: boolean | null;
  category: string | null;
  mode_of_travel: string | null;
  pickup_location: string | null;
  drop_location: string | null;
  property_used: string | null;
  activities: string[] | null;
  optional_activities: string[] | null;
  benefits: string[] | null;
  safety_supervision: string[] | null;
  things_to_carry: string[] | null;
  available_dates: string[] | null; // departure dates in YYYY-MM-DD format
  special_notes: string[] | null;
  payment_terms: string | null;
  cancellation_policy: any; // jsonb - can be object, array, or null
  auto_deactivation_date: string | null; // timestamp with time zone
  created_at: string | null;
  updated_at: string | null;
}

export interface TripItinerary {
  days: TripDay[];
}

export interface TripDay {
  day: number;
  title: string;
  subheading?: string;
  description: string;
  activities: string[];
  accommodation?: string;
  meals?: string[];
  location?: {
    name: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
}



export interface PaymentDetails {
  method: 'razorpay' | 'stripe' | 'bank_transfer';
  transaction_id?: string;
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded';
  amount_paid: number;
  payment_date?: string;
  gateway_response?: any;
}

// Testimonial interface for UI only (no database table)
export interface Testimonial {
  id: string;
  name: string;
  role?: string; // changed from user_id
  rating: number;
  title?: string;
  content: string;
  image_url?: string;
  is_approved?: boolean;
  is_featured?: boolean;
  // No database relationship fields
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  content: string;
  featured_image_url: string | null;
  author: string;
  category: string | null;
  tags: string[] | null;
  is_published: boolean | null;
  published_at: string | null;
  seo_title: string | null;
  seo_description: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface Inquiry {
  id: string;
  name: string;
  email: string;
  phone: string | null;
  subject: string | null;
  message: string;
  inquiry_type: string | null;
  trip_id: string | null;
  status: string | null; // USER-DEFINED enum
  admin_notes: string | null;
  responded_at: string | null;
  metadata: Record<string, any> | null; // jsonb
  created_at: string | null;
  updated_at: string | null;
  // Relations
  trip?: Trip;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  image_url: string | null;
  sort_order: number | null;
  is_active: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface TripPhotosDetail {
  id: string;
  trip_name: string;
  trip_description: string | null;
  featured_image_url: string | null;
  created_at: string | null;
  updated_at: string | null;
  storage_type: string | null; // USER-DEFINED enum
  google_photos_album_id: string | null;
  oauth_user_email: string | null;
  oauth_refresh_token: string | null;
  manual_shareable_url: string | null;
  access_password_hash: string | null;
  oauth_refresh_token_encrypted: string | null;
  security_version: number | null;
}

export interface NewsletterSubscription {
  id: string;
  email: string;
  name?: string;
  is_active: boolean;
  subscribed_at: string;
  unsubscribed_at?: string;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Filter and search types
export interface TripFilters {
  destination?: string;
  difficulty?: TripDifficulty;
  minPrice?: number;
  maxPrice?: number;
  minDuration?: number;
  maxDuration?: number;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}



// Form types
export interface CreateTripData {
  title: string;
  slug: string;
  description?: string;
  detailed_description?: string;
  destination: string;
  days: number;
  nights: number;
  min_age?: number;
  max_age?: number;
  price_per_person: number;
  difficulty: TripDifficulty;
  inclusions?: string[];
  exclusions?: string[];
  itinerary?: TripItinerary;
  featured_image_url?: string;
  is_trek?: boolean;
  is_active?: boolean;
  is_featured?: boolean;
  available_dates?: string[]; // departure dates in YYYY-MM-DD format
}



export interface CreateTestimonialData {
  trip_id?: string;
  name: string;
  email?: string;
  rating: number;
  title?: string;
  content: string;
  image_url?: string;
}

export interface CreateInquiryData {
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  message: string;
  inquiry_type?: string;
  trip_id?: string;
  metadata?: Record<string, any>;
}
