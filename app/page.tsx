import { Metadata } from 'next';
import { Suspense } from 'react';
import Footer from '@/components/layout/Footer';
import HomeHeader from '@/components/layout/HomeHeader';
import FeaturedTripsClient from '@/components/home/<USER>';
import FeaturedTripsSection from '@/components/sections/FeaturedTripsSection';
import AboutSection from '@/components/sections/AboutSection';
import TestimonialsSection from '@/components/sections/TestimonialsSection';
import ContactSection from '@/components/sections/ContactSection';
import InstagramFeed from '@/components/social/InstagramFeed';
import { PageErrorBoundary, SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import CacheManager from '@/components/cache/CacheManager';

import { COMPANY_INFO, TESTIMONIALS } from '@/lib/constants';
import { TripDifficulty } from '@/types/database';
import { createServerSupabase } from '@/lib/supabase-server';
import { SectionLoading } from '@/components/ui/LoadingStates';

// Minimal trip interface for homepage use
interface MinimalTrip {
  id: string;
  title: string;
  slug: string;
  description: string;
  destination: string;
  days: number;
  nights: number;
  price_per_person: number;
  difficulty: string;
  featured_image_url: string;
  is_featured: boolean;
  is_active: boolean;
}

export const metadata: Metadata = {
  title: 'Positive7 - Educational Tours and Student Travel Experiences',
  description: 'Experience unforgettable educational tours and student travel adventures with Positive7. We create unique learning journeys through adventure, exploration, and immersive experiences.',
  keywords: 'educational tours, student travel, adventure camps, experiential learning, school trips, international trips, CAS projects, workshops, picnics',
  alternates: {
    canonical: 'https://www.positive7.in',
  },
  openGraph: {
    title: 'Positive7 - Educational Tours and Student Travel Experiences',
    description: 'Experience unforgettable educational tours and student travel adventures with Positive7. We create unique learning journeys through adventure, exploration, and immersive experiences.',
    url: 'https://www.positive7.in',
    siteName: 'Positive7',
    images: [
      {
        url: 'https://www.positive7.in/images/og-home.jpg',
        width: 1200,
        height: 630,
        alt: 'Positive7 Educational Tours',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Positive7 - Educational Tours and Student Travel Experiences',
    description: 'Experience unforgettable educational tours and student travel adventures with Positive7. We create unique learning journeys through adventure, exploration, and immersive experiences.',
    images: ['https://www.positive7.in/images/og-home.jpg'],
    creator: '@positive7_in',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// Disable caching for real-time data
export const revalidate = 0;

// Fetch featured trips
async function getFeaturedTrips(): Promise<MinimalTrip[]> {
  const supabase = createServerSupabase();
  
  const { data: trips, error } = await supabase
    .from('trips')
    .select(`
      id,
      title,
      slug,
      description,
      destination,
      days,
      nights,
      price_per_person,
      difficulty,
      featured_image_url,
      is_featured,
      is_active
    `)
    .eq('is_active', true)
    .eq('is_featured', true)
    .order('created_at', { ascending: false })
    .limit(6);
    
  if (error) {
    console.error('Error fetching featured trips:', error);
    return [];
  }
  
  // Transform trips to ensure proper type safety
  return trips.map(trip => ({
    id: trip.id,
    title: trip.title,
    slug: trip.slug,
    description: trip.description || '',
    destination: trip.destination,
    days: trip.days || 0,
    nights: trip.nights || 0,
    price_per_person: trip.price_per_person,
    difficulty: trip.difficulty || 'moderate',
    featured_image_url: trip.featured_image_url || '/images/fallback-trip.jpg',
    is_featured: trip.is_featured === true,
    is_active: trip.is_active === true
  }));
}

// Get hardcoded testimonials from constants
function getHardcodedTestimonials() {
  // Convert from constants format to expected testimonial format
  return TESTIMONIALS.slice(0, 6).map(testimonial => ({
    id: testimonial.id.toString(),
    name: testimonial.name,
    rating: testimonial.rating,
    title: testimonial.role,
    content: testimonial.content,
    image_url: testimonial.image,
    is_featured: true,
    is_approved: true
  }));
}

export default async function HomePage() {
  // Fetch initial data from Supabase for SSR
  const featuredTrips = await getFeaturedTrips();
  const testimonials = getHardcodedTestimonials();

  return (
    <PageErrorBoundary context="home-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <HomeHeader isFallbackMode={featuredTrips.length === 0} />
      <main className="flex-1">
        {/* Hero Section with Real-Time Updates */}
        <SectionErrorBoundary context="hero-section">
          <Suspense fallback={<SectionLoading message="Loading hero trips..." />}>
            <FeaturedTripsClient initialTrips={featuredTrips} />
          </Suspense>
        </SectionErrorBoundary>

        {/* Featured Trips Section */}
        <SectionErrorBoundary context="featured-trips">
          <Suspense fallback={<SectionLoading message="Loading featured trips..." />}>
            <FeaturedTripsSection initialFeaturedTrips={featuredTrips} />
          </Suspense>
        </SectionErrorBoundary>

        {/* About Section */}
        <SectionErrorBoundary context="about-section">
          <AboutSection />
        </SectionErrorBoundary>

        {/* Testimonials Section */}
        <SectionErrorBoundary context="testimonials">
          <TestimonialsSection testimonials={testimonials} />
        </SectionErrorBoundary>

        {/* Instagram Feed Section */}
        <SectionErrorBoundary context="instagram-feed">
          <section className="py-20 bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50">
            <div className="container-custom">
              <InstagramFeed maxPosts={6} />
            </div>
          </section>
        </SectionErrorBoundary>

        {/* Contact Section */}
        <SectionErrorBoundary context="contact-section">
          <ContactSection />
        </SectionErrorBoundary>
      </main>

      <Footer />

      {/* Structured Data for Homepage */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Organization',
            name: COMPANY_INFO.name,
            alternateName: 'Positive Seven',
            description: COMPANY_INFO.description,
            url: COMPANY_INFO.website,
            logo: `${COMPANY_INFO.website}/images/positive7-logo.png`,
            image: `${COMPANY_INFO.website}/images/og-home.jpg`,
            telephone: COMPANY_INFO.phone,
            email: COMPANY_INFO.email,
            address: {
              '@type': 'PostalAddress',
              streetAddress: '904, Shivalik Highstreet, B/S ITC Narmada Hotel',
              addressLocality: 'Vastrapur, Ahmedabad',
              addressRegion: 'Gujarat',
              postalCode: '380015',
              addressCountry: 'IN',
            },
            geo: {
              '@type': 'GeoCoordinates',
              latitude: '23.0225',
              longitude: '72.5714',
            },
            sameAs: [
              'https://www.facebook.com/positive7.ind',
              'https://www.instagram.com/positive.seven/',
              'https://www.youtube.com/channel/UC22w2efe7oZCmEcrU8g2xnw/featured',
            ],
            serviceType: [
              'Educational Tours',
              'Student Travel',
              'Adventure Camps',
              'Experiential Learning',
              'School Trips',
              'CAS Projects',
              'Workshops',
              'Picnics',
            ],
            areaServed: [
              {
                '@type': 'Country',
                name: 'India',
              },
            ],
            hasOfferCatalog: {
              '@type': 'OfferCatalog',
              name: 'Educational Tours and Student Travel Services',
              itemListElement: [
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'Manali Educational Tour',
                    description: 'Snow-capped peaks and adventure learning experience in Himachal Pradesh',
                  },
                },
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'Rishikesh Spiritual Tour',
                    description: 'Yoga and spiritual learning experience in the Yoga Capital of the World',
                  },
                },
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'Adventure Camps',
                    description: 'Outdoor adventure and team building camps for students',
                  },
                },
                {
                  '@type': 'Offer',
                  itemOffered: {
                    '@type': 'Service',
                    name: 'CAS Projects',
                    description: 'Creativity, Activity, Service projects for IB students',
                  },
                },
              ],
            },
          }),
        }}
      />
    </PageErrorBoundary>
  );
}
