'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';
import { CreatePhotoAlbumRequest } from '@/types/photo-album';
import { useToast } from '@/hooks/useToast';
import {
  ArrowLeft,
  Camera,
  Save,
  Loader2,
  CheckCircle,
  ArrowRight,
  ExternalLink,
  AlertCircle
} from 'lucide-react';
import { useCreatePhotoAlbum, useUpdatePhotoAlbum } from '@/hooks/usePhotoAlbums';

type Step = 'basic-info' | 'oauth-setup' | 'shareable-link';

interface StepData {
  basicInfo: CreatePhotoAlbumRequest;
  oauthCompleted: boolean;
  albumId?: string;
  manualShareableUrl: string;
}

export default function MultiStepAlbumForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const toast = useToast();
  const [currentStep, setCurrentStep] = useState<Step>('basic-info');
  const [errors, setErrors] = useState<{
    trip_name?: string;
    access_password?: string;
    manualShareableUrl?: string;
  }>({});
  const [stepData, setStepData] = useState<StepData>({
    basicInfo: {
      trip_name: '',
      trip_description: '',
      featured_image_url: '',
      access_password: '',
    },
    oauthCompleted: false,
    manualShareableUrl: '',
  });

  // React Query hooks
  const createPhotoAlbumMutation = useCreatePhotoAlbum();
  const updatePhotoAlbumMutation = useUpdatePhotoAlbum();

  // Handle URL parameters for step navigation
  useEffect(() => {
    const step = searchParams.get('step') as Step;
    const albumId = searchParams.get('albumId');
    const success = searchParams.get('success');

    if (step === 'shareable-link' && albumId) {
      setStepData(prev => ({
        ...prev,
        albumId,
        oauthCompleted: true
      }));
      setCurrentStep('shareable-link');

      if (success) {
        toast.success(success);
      }
    }
  }, [searchParams]); // Removed toast from dependencies to prevent infinite re-renders

  const handleBasicInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Clear error for this field when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    setStepData(prev => ({
      ...prev,
      basicInfo: {
        ...prev.basicInfo,
        [name]: value
      }
    }));
  };

  const handleImageUpload = (url: string) => {
    setStepData(prev => ({
      ...prev,
      basicInfo: {
        ...prev.basicInfo,
        featured_image_url: url
      }
    }));
  };

  const handleBasicInfoSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setErrors({});

    const newErrors: { trip_name?: string; access_password?: string } = {};

    // Validate trip name
    if (!stepData.basicInfo.trip_name.trim()) {
      newErrors.trip_name = 'Trip name is required';
    }

    // Validate password if provided
    if (stepData.basicInfo.access_password && stepData.basicInfo.access_password.trim()) {
      if (stepData.basicInfo.access_password.trim().length < 4) {
        newErrors.access_password = 'Password must be at least 4 characters long';
      }
    }

    // If there are errors, set them and return
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Just move to next step without creating database entry yet
    toast.success('Basic information saved! Now setting up Google Photos...');
    setCurrentStep('oauth-setup');
  };

  const handleOAuthSetup = async () => {
    const toastId = toast.loading('Creating album and setting up OAuth...');

    try {
      // Create the database entry using React Query
      const result = await createPhotoAlbumMutation.mutateAsync(stepData.basicInfo);

      setStepData(prev => ({
        ...prev,
        albumId: result.id
      }));

      toast.dismiss(toastId);
      toast.success('Album created! Redirecting to Google Photos authorization...');

      // Small delay to show success message before redirect
      setTimeout(() => {
        window.location.href = `/api/auth/google?albumId=${result.id}`;
      }, 1000);

    } catch (error) {
      console.error('Error creating album:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create album');
      toast.dismiss(toastId);
    }
  };

  const handleShareableLinkSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setErrors(prev => ({ ...prev, manualShareableUrl: undefined }));

    let linkError = '';

    if (!stepData.manualShareableUrl.trim()) {
      linkError = 'Shareable link is required';
    } else {
      // Validate Google Photos URL format (supports both old and new formats)
      const googlePhotosUrlPattern = /^https:\/\/(photos\.google\.com\/share\/[A-Za-z0-9_-]+|photos\.app\.goo\.gl\/[A-Za-z0-9]+)$/;
      if (!googlePhotosUrlPattern.test(stepData.manualShareableUrl)) {
        linkError = 'Please enter a valid Google Photos shareable link (photos.google.com/share/... or photos.app.goo.gl/...)';
      }
    }

    // If there's an error, set it and return
    if (linkError) {
      setErrors(prev => ({ ...prev, manualShareableUrl: linkError }));
      return;
    }

    const toastId = toast.loading('Saving shareable link...');

    try {
      await updatePhotoAlbumMutation.mutateAsync({
        id: stepData.albumId!,
        data: {
          manual_shareable_url: stepData.manualShareableUrl.trim()
        }
      });

      toast.success('Album setup completed successfully!');
      router.push(`/admin/trips-photos/${stepData.albumId}`);

    } catch (error) {
      console.error('Error saving shareable link:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save shareable link');
    } finally {
      toast.dismiss(toastId);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      <div className="flex items-center space-x-4">
        {/* Step 1 */}
        <div className={`flex items-center ${currentStep === 'basic-info' ? 'text-purple-600' : currentStep === 'oauth-setup' || currentStep === 'shareable-link' ? 'text-green-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep === 'basic-info' ? 'bg-purple-100 text-purple-600' : currentStep === 'oauth-setup' || currentStep === 'shareable-link' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
            {currentStep === 'oauth-setup' || currentStep === 'shareable-link' ? <CheckCircle className="w-5 h-5" /> : '1'}
          </div>
          <span className="ml-2 text-sm font-medium">Basic Info</span>
        </div>

        <ArrowRight className="w-4 h-4 text-gray-400" />

        {/* Step 2 */}
        <div className={`flex items-center ${currentStep === 'oauth-setup' ? 'text-purple-600' : currentStep === 'shareable-link' ? 'text-green-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep === 'oauth-setup' ? 'bg-purple-100 text-purple-600' : currentStep === 'shareable-link' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
            {currentStep === 'shareable-link' ? <CheckCircle className="w-5 h-5" /> : '2'}
          </div>
          <span className="ml-2 text-sm font-medium">OAuth Setup</span>
        </div>

        <ArrowRight className="w-4 h-4 text-gray-400" />

        {/* Step 3 */}
        <div className={`flex items-center ${currentStep === 'shareable-link' ? 'text-purple-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep === 'shareable-link' ? 'bg-purple-100 text-purple-600' : 'bg-gray-100 text-gray-400'}`}>
            3
          </div>
          <span className="ml-2 text-sm font-medium">Shareable Link</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Link
          href="/admin/trips-photos"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Photo Albums
        </Link>
        <div className="flex items-center">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mr-4">
            <Camera className="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create New Photo Album</h1>
            <p className="text-gray-600">Set up a new Google Photos album with shareable link</p>
          </div>
        </div>
      </div>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Step Content */}
      <div className="bg-white shadow-sm rounded-lg">
        {currentStep === 'basic-info' && (
          <form onSubmit={handleBasicInfoSubmit} className="p-6 space-y-6">
            <div className="border-b border-gray-200 pb-4">
              <h2 className="text-lg font-semibold text-gray-900">Step 1: Basic Information</h2>
              <p className="text-sm text-gray-600 mt-1">Enter the basic details for your photo album</p>
            </div>

            {/* Trip Name */}
            <div>
              <label htmlFor="trip_name" className="block text-sm font-medium text-gray-700 mb-1">
                Trip Name *
              </label>
              <input
                type="text"
                id="trip_name"
                name="trip_name"
                value={stepData.basicInfo.trip_name}
                onChange={handleBasicInfoChange}
                required
                placeholder="Enter trip name (e.g., Bali Adventure 2024)"
                className={`w-full rounded-md shadow-sm focus:ring-purple-500 ${
                  errors.trip_name
                    ? 'border-red-300 focus:border-red-500'
                    : 'border-gray-300 focus:border-purple-500'
                }`}
              />
              {errors.trip_name && (
                <p className="mt-1 text-sm text-red-600">{errors.trip_name}</p>
              )}
            </div>

            {/* Trip Description */}
            <div>
              <label htmlFor="trip_description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="trip_description"
                name="trip_description"
                value={stepData.basicInfo.trip_description}
                onChange={handleBasicInfoChange}
                rows={3}
                placeholder="Brief description of the trip..."
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
              />
            </div>

            {/* Featured Image */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Featured Image
              </label>
              <CloudinaryUpload
                onUpload={handleImageUpload}
                currentImage={stepData.basicInfo.featured_image_url}
                uploadType="trip"
                entityId="new"
              />
            </div>

            {/* Access Password */}
            <div>
              <label htmlFor="access_password" className="block text-sm font-medium text-gray-700 mb-1">
                Access Password (Optional)
              </label>
              <input
                type="password"
                id="access_password"
                name="access_password"
                value={stepData.basicInfo.access_password || ''}
                onChange={handleBasicInfoChange}
                placeholder="Leave empty for public access"
                className={`w-full rounded-md shadow-sm focus:ring-purple-500 ${
                  errors.access_password
                    ? 'border-red-300 focus:border-red-500'
                    : 'border-gray-300 focus:border-purple-500'
                }`}
              />
              {errors.access_password && (
                <p className="mt-1 text-sm text-red-600">{errors.access_password}</p>
              )}

              {/* Password Security Warning */}
              <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-amber-800 mb-1">⚠️ Important Password Security Notice</p>
                    <ul className="text-amber-700 space-y-1 text-xs">
                      <li>• <strong>Write down this password immediately</strong> - you cannot view it later</li>
                      <li>• Passwords are securely encrypted and cannot be recovered</li>
                      <li>• If forgotten, you'll need to create a new password (users will need the new one)</li>
                      <li>• Minimum 4 characters required for security</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <Link
                href="/admin/trips-photos"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={!stepData.basicInfo.trip_name.trim()}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowRight className="h-4 w-4 mr-2" />
                Continue to OAuth Setup
              </button>
            </div>
          </form>
        )}

        {currentStep === 'oauth-setup' && (
          <div className="p-6 space-y-6">
            <div className="border-b border-gray-200 pb-4">
              <h2 className="text-lg font-semibold text-gray-900">Step 2: Google Photos OAuth Setup</h2>
              <p className="text-sm text-gray-600 mt-1">Connect your Google Photos account to enable photo uploads</p>
            </div>

            <div className="text-center py-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <Camera className="w-8 h-8 text-blue-600" />
              </div>

              <h3 className="text-lg font-medium text-gray-900 mb-2">Connect Google Photos</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                You'll be redirected to Google to authorize access to your Photos account.
                This allows our system to create albums and upload photos.
              </p>

              <button
                onClick={handleOAuthSetup}
                disabled={createPhotoAlbumMutation.isPending}
                className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {createPhotoAlbumMutation.isPending ? (
                  <>
                    <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                    Creating Album...
                  </>
                ) : (
                  <>
                    <ExternalLink className="h-5 w-5 mr-2" />
                    Connect Google Photos
                  </>
                )}
              </button>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-900">Important</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    After completing OAuth authorization, you'll be redirected back to continue with the shareable link setup.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentStep === 'shareable-link' && (
          <form onSubmit={handleShareableLinkSubmit} className="p-6 space-y-6">
            <div className="border-b border-gray-200 pb-4">
              <h2 className="text-lg font-semibold text-gray-900">Step 3: Add Shareable Link</h2>
              <p className="text-sm text-gray-600 mt-1">Create a shareable link in Google Photos and add it here</p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-green-900">OAuth Setup Complete</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Google Photos account connected successfully. The album has been created and is ready for uploads.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <Camera className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-blue-900">How to get the shareable link:</h4>
                  <div className="text-sm text-blue-700 mt-1 space-y-1">
                    <p>1. Go to <a href="https://photos.google.com" target="_blank" rel="noopener noreferrer" className="underline">photos.google.com</a></p>
                    <p>2. Find the album that was just created: "{stepData.basicInfo.trip_name} - Photos"</p>
                    <p>3. Click the "Create link" button in the album</p>
                    <p>4. Copy the shareable link and paste it below</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="manual_shareable_url" className="block text-sm font-medium text-gray-700 mb-1">
                Google Photos Shareable Link *
              </label>
              <input
                type="url"
                id="manual_shareable_url"
                value={stepData.manualShareableUrl}
                onChange={(e) => {
                  // Clear error when user starts typing
                  if (errors.manualShareableUrl) {
                    setErrors(prev => ({ ...prev, manualShareableUrl: undefined }));
                  }
                  setStepData(prev => ({ ...prev, manualShareableUrl: e.target.value }));
                }}
                required
                placeholder="https://photos.app.goo.gl/... or https://photos.google.com/share/..."
                className={`w-full rounded-md shadow-sm focus:ring-purple-500 ${
                  errors.manualShareableUrl
                    ? 'border-red-300 focus:border-red-500'
                    : 'border-gray-300 focus:border-purple-500'
                }`}
              />
              {errors.manualShareableUrl && (
                <p className="mt-1 text-sm text-red-600">{errors.manualShareableUrl}</p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                This link will be used on the public trips-photos page for users to access the album. Supports both photos.app.goo.gl and photos.google.com/share formats.
              </p>
            </div>

            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => setCurrentStep('oauth-setup')}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={updatePhotoAlbumMutation.isPending || !stepData.manualShareableUrl.trim()}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {updatePhotoAlbumMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Complete Album Setup
                  </>
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
