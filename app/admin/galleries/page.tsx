'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/components/layout/AdminLayout';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import {
  Plus,
  Search,
  Filter,
  Upload,
  Edit,
  Trash2,
  ImageIcon,
  Calendar,
  MapPin,
  MoreVertical
} from 'lucide-react';
import { Gallery } from '@/types/gallery';
import { useGalleries, useDeleteGallery, GalleryQueryParams } from '@/hooks/useGalleries';
import { handleClientError } from '@/lib/error-handler';
import { useToast } from '@/hooks/useToast';
import { useDebounce } from '@/hooks/useDebounce';

export default function GalleriesPage() {
  const router = useRouter();
  const toast = useToast();

  // Filters and pagination state
  const [search, setSearch] = useState('');
  const [isActive, setIsActive] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
  });

  // Delete modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [galleryToDelete, setGalleryToDelete] = useState<Gallery | null>(null);

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(search, 300);

  // Use React Query for data fetching with debounced search
  const queryParams: GalleryQueryParams = {
    page: pagination.page,
    limit: pagination.limit,
    ...(debouncedSearch && { search: debouncedSearch }),
    ...(isActive !== 'all' && { is_active: isActive }),
  };

  const {
    data: galleriesData,
    isLoading: loading,
    error: queryError,
    refetch,
  } = useGalleries(queryParams);

  const deleteGalleryMutation = useDeleteGallery();

  const galleries = galleriesData?.data || [];
  const paginationData = galleriesData?.pagination || { page: 1, limit: 10, total: 0, totalPages: 0 };
  const error = queryError ? handleClientError(queryError, 'fetching galleries') : null;

  // Handle search and filter changes
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on search
  };

  const handleStatusChange = (value: string) => {
    setIsActive(value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on filter change
  };

  const handleClearFilters = () => {
    setSearch('');
    setIsActive('all');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleDelete = (gallery: Gallery) => {
    setGalleryToDelete(gallery);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!galleryToDelete) return;

    try {
      await deleteGalleryMutation.mutateAsync(galleryToDelete.id);
      setShowDeleteModal(false);
      setGalleryToDelete(null);
      // Success and error toasts are handled by the hook
    } catch (error) {
      console.error('Error deleting gallery:', error);
      // Error toast is handled by the hook
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setGalleryToDelete(null);
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (loading && galleries.length === 0) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Gallery Management</h1>
        <Button
          onClick={() => router.push('/admin/galleries/new')}
          className="flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add New Gallery
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search galleries..."
                value={search}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={isActive}
              onChange={(e) => handleStatusChange(e.target.value)}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="all">All Galleries</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button
              onClick={handleClearFilters}
              variant="outline"
              className="w-full"
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
          {error}
        </div>
      )}

      {/* Galleries Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {galleries.map((gallery) => (
          <div key={gallery.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            {/* Gallery Image */}
            <div className="relative h-48 bg-gray-200">
              {gallery.featured_image_url ? (
                <Image
                  src={gallery.featured_image_url}
                  alt={gallery.name}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <ImageIcon className="h-12 w-12 text-gray-400" />
                </div>
              )}
              
              {/* Status Badge */}
              <div className="absolute top-2 left-2">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  gallery.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {gallery.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              {/* Image Count Badge */}
              <div className="absolute top-2 right-2">
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                  {gallery.image_count} images
                </span>
              </div>
            </div>

            {/* Gallery Info */}
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{gallery.name}</h3>
              
              {gallery.description && (
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {gallery.description}
                </p>
              )}

              {gallery.trips && (
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <MapPin className="h-4 w-4 mr-1" />
                  {gallery.trips.title} - {gallery.trips.destination}
                </div>
              )}

              <div className="flex items-center text-sm text-gray-500 mb-4">
                <Calendar className="h-4 w-4 mr-1" />
                {gallery.created_at ? new Date(gallery.created_at).toLocaleDateString() : 'No date'}
              </div>

              {/* Actions */}
              <div className="flex justify-between items-center">
                <div className="flex space-x-2">
                  <Link
                    href={`/admin/galleries/${gallery.id}/edit`}
                    className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-gray-100"
                    title="Edit Gallery"
                  >
                    <Edit className="h-4 w-4" />
                  </Link>
                  <button
                    onClick={() => handleDelete(gallery)}
                    className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-gray-100"
                    title="Delete Gallery"
                    disabled={deleteGalleryMutation.isPending}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
                <div>
                  <Link
                    href={`/admin/galleries/${gallery.id}`}
                    className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                    title="Upload Images"
                  >
                    <Upload className="h-4 w-4 mr-1" />
                    Upload
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {galleries.length === 0 && !loading && (
        <div className="text-center py-12">
          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No galleries found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new gallery.
          </p>
          <div className="mt-6">
            <Button onClick={() => router.push('/admin/galleries/new')}>
              <Plus className="h-5 w-5 mr-2" />
              Add New Gallery
            </Button>
          </div>
        </div>
      )}

      {/* Pagination */}
      {paginationData.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-6">
          <Button
            onClick={() => handlePageChange(paginationData.page - 1)}
            disabled={paginationData.page === 1 || loading}
            variant="outline"
            size="sm"
          >
            Previous
          </Button>

          <span className="text-sm text-gray-700">
            Page {paginationData.page} of {paginationData.totalPages}
          </span>

          <Button
            onClick={() => handlePageChange(paginationData.page + 1)}
            disabled={paginationData.page === paginationData.totalPages || loading}
            variant="outline"
            size="sm"
          >
            Next
          </Button>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {galleryToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          title="Delete Gallery"
          message={`Are you sure you want to delete "${galleryToDelete.name}"?\n\nThis action cannot be undone and will permanently remove the gallery, all its images, and the associated Cloudinary folder.`}
          confirmText="Delete Gallery"
          variant="danger"
          loading={deleteGalleryMutation.isPending}
        />
      )}
    </AdminLayout>
  );
}
