'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import AdminLayout from '@/components/layout/AdminLayout';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import GalleryImageUpload from '@/components/ui/GalleryImageUpload';
import GalleryQueueUpload from '@/components/ui/GalleryQueueUpload';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Plus,
  ImageIcon,
  Calendar,
  MapPin,
  Eye,
  Upload,
  X
} from 'lucide-react';
import { GalleryWithImages, GalleryImage } from '@/types/gallery';
import { useToast } from '@/hooks/useToast';
import { useGallery, useGalleryImages, useDeleteGallery, useUploadGalleryImage, useDeleteGalleryImage, useBatchUploadGalleryImages, GALLERY_QUERY_KEYS } from '@/hooks/useGalleries';
import { useQueryClient } from '@tanstack/react-query';

interface GalleryDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function GalleryDetailPage({ params }: GalleryDetailPageProps) {
  const router = useRouter();
  const toast = useToast();
  const queryClient = useQueryClient();
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [showQueueUpload, setShowQueueUpload] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showImageDeleteModal, setShowImageDeleteModal] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<{ id: string; caption?: string } | null>(null);

  const [galleryId, setGalleryId] = useState<string>('');

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setGalleryId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  // React Query hooks
  const { data: gallery, isLoading: loading, error } = useGallery(galleryId, !!galleryId);
  const { data: galleryImages, isLoading: imagesLoading } = useGalleryImages(galleryId, !!galleryId);
  const deleteGalleryMutation = useDeleteGallery();
  const uploadImageMutation = useUploadGalleryImage();
  const deleteImageMutation = useDeleteGalleryImage();
  const batchUploadMutation = useBatchUploadGalleryImages();

  // No need for manual fetch function - React Query handles this

  const handleDeleteGallery = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!gallery) return;

    try {
      await deleteGalleryMutation.mutateAsync(gallery.id);
      toast.success('Gallery deleted successfully');
      router.push('/admin/galleries');
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to delete gallery');
    } finally {
      setShowDeleteModal(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  const handleDeleteImage = (imageId: string, caption?: string) => {
    setImageToDelete({ id: imageId, caption });
    setShowImageDeleteModal(true);
  };

  const handleDeleteImageConfirm = async () => {
    if (!gallery || !imageToDelete) return;

    try {
      await deleteImageMutation.mutateAsync({
        galleryId: gallery.id,
        imageId: imageToDelete.id
      });
      toast.success('Image deleted successfully');
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to delete image');
    } finally {
      setShowImageDeleteModal(false);
      setImageToDelete(null);
    }
  };

  const handleDeleteImageCancel = () => {
    setShowImageDeleteModal(false);
    setImageToDelete(null);
  };

  const handleImageUpload = async (data: { imageUrl: string }) => {
    if (!gallery) return;

    try {
      // Extract public_id from Cloudinary URL
      const urlParts = data.imageUrl.split('/');
      const publicIdWithExtension = urlParts[urlParts.length - 1];
      const publicId = publicIdWithExtension.split('.')[0];
      const folderPath = urlParts.slice(7, -1).join('/'); // Extract folder path
      const fullPublicId = folderPath ? `${folderPath}/${publicId}` : publicId;

      await uploadImageMutation.mutateAsync({
        galleryId: gallery.id,
        formData: (() => {
          const formData = new FormData();
          formData.append('image_url', data.imageUrl);
          formData.append('cloudinary_public_id', fullPublicId);
          return formData;
        })()
      });

      toast.success('Image added successfully');
      setShowUploadForm(false);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to add image');
      throw err; // Re-throw to let the upload component handle it
    }
  };

  const handleQueueUpload = async (uploadedImages: Array<{ imageUrl: string; cloudinaryPublicId: string }>) => {
    if (!gallery) return;

    try {
      await batchUploadMutation.mutateAsync({
        galleryId: gallery.id,
        images: uploadedImages
      });
      setShowQueueUpload(false);
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to save images to gallery');
      throw err;
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AdminLayout>
    );
  }

  if (error || !gallery) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Gallery not found</h3>
          <p className="text-gray-500 mb-4">{error instanceof Error ? error.message : String(error) || 'The requested gallery could not be found.'}</p>
          <Link href="/admin/galleries">
            <Button>Back to Galleries</Button>
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {/* Header */}
      <div className="mb-6">
        <Link
          href="/admin/galleries"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Galleries
        </Link>
        
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{gallery.name}</h1>
            {gallery.description && (
              <p className="text-gray-600 mt-1">{gallery.description}</p>
            )}
            
            <div className="flex items-center space-x-4 mt-3">
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                gallery.is_active 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {gallery.is_active ? 'Active' : 'Inactive'}
              </span>
              
              {gallery.trips && (
                <div className="flex items-center text-sm text-gray-500">
                  <MapPin className="h-4 w-4 mr-1" />
                  {gallery.trips.title} - {gallery.trips.destination}
                </div>
              )}
              
              <div className="flex items-center text-sm text-gray-500">
                <Calendar className="h-4 w-4 mr-1" />
                {gallery.created_at ? new Date(gallery.created_at).toLocaleDateString() : 'No date'}
              </div>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Link href={`/admin/galleries/${gallery.id}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
            <Button
              onClick={handleDeleteGallery}
              variant="outline"
              className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Images Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Gallery Images ({galleryImages?.length || 0})
          </h2>
          <div className="flex space-x-2">
            <Button
              onClick={() => {
                setShowUploadForm(!showUploadForm);
                setShowQueueUpload(false);
              }}
              variant={showUploadForm ? "primary" : "outline"}
              className="flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Single Upload
            </Button>
            <Button
              onClick={() => {
                setShowQueueUpload(!showQueueUpload);
                setShowUploadForm(false);
              }}
              variant={showQueueUpload ? "primary" : "outline"}
              className="flex items-center"
            >
              <Upload className="h-4 w-4 mr-2" />
              Batch Upload
            </Button>
          </div>
        </div>

        {/* Single Upload Form */}
        {showUploadForm && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Upload Single Image</h3>
              <button
                onClick={() => setShowUploadForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <GalleryImageUpload
              onUpload={handleImageUpload}
              folder={`positive7/galleries/${gallery.folder_name || gallery.name.toLowerCase().replace(/[^a-z0-9]+/g, '-')}`}
              placeholder="Select image to upload to gallery"
            />
          </div>
        )}

        {/* Queue Upload Form */}
        {showQueueUpload && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Batch Upload Images</h3>
              <button
                onClick={() => setShowQueueUpload(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <GalleryQueueUpload
              onUploadComplete={handleQueueUpload}
              folder={`positive7/galleries/${gallery.folder_name || gallery.name.toLowerCase().replace(/[^a-z0-9]+/g, '-')}`}
            />
          </div>
        )}

        {/* Images Grid */}
        {galleryImages && galleryImages.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {galleryImages.map((image: any) => (
              <div key={image.id} className="relative group">
                <div className="relative aspect-square bg-gray-200 rounded-lg overflow-hidden">
                  <Image
                    src={image.image_url}
                    alt={gallery.name}
                    fill
                    className="object-cover"
                  />
                  
                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                      <button
                        onClick={() => window.open(image.image_url, '_blank')}
                        className="p-2 bg-white rounded-full text-gray-700 hover:text-blue-600"
                        title="View Full Size"
                      >
                        <Eye className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => handleDeleteImage(image.id, '')}
                        className="p-2 bg-white rounded-full text-gray-700 hover:text-red-600"
                        title="Delete Image"
                        disabled={deleteImageMutation.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No images yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by uploading some images to this gallery.
            </p>
            <div className="mt-6">
              <Button onClick={() => setShowUploadForm(true)}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Images
              </Button>
            </div>
          </div>
        )}

        {/* Delete Gallery Confirmation Modal */}
        {gallery && (
          <ConfirmationModal
            isOpen={showDeleteModal}
            onClose={handleDeleteCancel}
            onConfirm={handleDeleteConfirm}
            title="Delete Gallery"
            message={`Are you sure you want to delete "${gallery.name}"?\n\nThis action cannot be undone and will permanently remove the gallery, all its images, and the associated Cloudinary folder.`}
            confirmText="Delete Gallery"
            variant="danger"
            loading={deleteGalleryMutation.isPending}
          />
        )}

        {/* Delete Image Confirmation Modal */}
        {imageToDelete && (
          <ConfirmationModal
            isOpen={showImageDeleteModal}
            onClose={handleDeleteImageCancel}
            onConfirm={handleDeleteImageConfirm}
            title="Delete Image"
            message={`Are you sure you want to delete this image?\n\nThis action cannot be undone and will permanently remove the image from the gallery and Cloudinary.`}
            confirmText="Delete Image"
            variant="danger"
            loading={deleteImageMutation.isPending}
          />
        )}
      </div>
    </AdminLayout>
  );
}
