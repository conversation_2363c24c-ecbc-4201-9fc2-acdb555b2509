'use client';

import { useState } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { usePerformance, usePerformanceActions } from '@/hooks/usePerformance';
import { ErrorBoundary } from '@/components/error/ErrorBoundary';
import AdminLayout from '@/components/layout/AdminLayout';
import { 
  Activity, 
  Server, 
  Database, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Trash2,
  RefreshCw,
  BarChart3,
  Monitor,
  HardDrive,
  Cpu
} from 'lucide-react';

function PerformancePage() {
  const { adminUser, hasPermission, loading } = useAuth();
  const [timeRange, setTimeRange] = useState(3600000); // 1 hour default
  const [includeCache, setIncludeCache] = useState(true);
  const [includeDetails, setIncludeDetails] = useState(true);
  
  const { data: performanceData, isLoading, error, refetch } = usePerformance({
    timeRange,
    includeCache,
    includeDetails,
  });
  
  const performanceActions = usePerformanceActions();

  // Check permissions
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!adminUser || !hasPermission('performance', 'read')) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to view performance data.</p>
        </div>
      </div>
    );
  }

  const handleAction = (action: string) => {
    performanceActions.mutate({ action }, {
      onSuccess: () => {
        refetch();
      }
    });
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    return <XCircle className="h-5 w-5 text-red-600" />;
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Performance Data</h2>
          <p className="text-gray-600 mb-4">{error.message}</p>
          <button
            onClick={() => refetch()}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Performance Dashboard</h1>
            <p className="text-gray-600">Monitor system performance and manage caches</p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Time Range Selector */}
            <label htmlFor="time-range-selector" className="sr-only">
              Select time range for performance data
            </label>
            <select
              id="time-range-selector"
              value={timeRange}
              onChange={(e) => setTimeRange(Number(e.target.value))}
              className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
            <option value={300000}>5 minutes</option>
            <option value={900000}>15 minutes</option>
            <option value={3600000}>1 hour</option>
            <option value={21600000}>6 hours</option>
            <option value={86400000}>24 hours</option>
          </select>
          
            <button
              onClick={() => refetch()}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {performanceData && (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Health Score */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {getHealthScoreIcon(performanceData.summary.healthScore)}
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Health Score</dt>
                      <dd className={`text-lg font-medium ${getHealthScoreColor(performanceData.summary.healthScore)}`}>
                        {performanceData.summary.healthScore}%
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Total Metrics */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Total Metrics</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {performanceData.summary.totalMetrics.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Average Response Time */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Clock className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Avg Response Time</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {performanceData.summary.averageResponseTime.toFixed(0)}ms
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Threshold Violations */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Violations</dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {performanceData.summary.totalThresholdViolations}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

        {/* System Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  <Monitor className="inline h-5 w-5 mr-2" />
                  System Information
                </h3>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Node Version</dt>
                    <dd className="mt-1 text-sm text-gray-900">{performanceData.systemInfo.nodeVersion}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Platform</dt>
                    <dd className="mt-1 text-sm text-gray-900">{performanceData.systemInfo.platform}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Uptime</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatUptime(performanceData.systemInfo.uptime)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(performanceData.systemInfo.timestamp).toLocaleString()}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Memory Usage */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  <HardDrive className="inline h-5 w-5 mr-2" />
                  Memory Usage
                </h3>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">RSS</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.memoryUsage.rss)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Heap Total</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.memoryUsage.heapTotal)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Heap Used</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.memoryUsage.heapUsed)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">External</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatBytes(performanceData.systemInfo.memoryUsage.external)}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>

        {/* Performance Metrics */}
        <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                <Activity className="inline h-5 w-5 mr-2" />
                Performance Metrics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <dt className="text-sm font-medium text-gray-500">API Response Time</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">
                    {performanceData.summary.averageResponseTime.toFixed(0)}ms
                  </dd>
                  <div className="mt-1 text-xs text-gray-500">
                    Threshold: {performanceData.performance.thresholds.api_response_time.warning}ms
                  </div>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Database Query Time</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">
                    {performanceData.summary.averageDbQueryTime.toFixed(0)}ms
                  </dd>
                  <div className="mt-1 text-xs text-gray-500">
                    Threshold: {performanceData.performance.thresholds.database_query.warning}ms
                  </div>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Page Load Time</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">
                    {performanceData.summary.averagePageLoadTime.toFixed(0)}ms
                  </dd>
                  <div className="mt-1 text-xs text-gray-500">
                    Threshold: {performanceData.performance.thresholds.page_load.warning}ms
                  </div>
                </div>
              </div>
            </div>
          </div>

        {/* Actions */}
        {hasPermission('performance', 'update') && (
          <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  <Cpu className="inline h-5 w-5 mr-2" />
                  Performance Actions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <button
                    onClick={() => handleAction('clear_metrics')}
                    disabled={performanceActions.isPending}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Metrics
                  </button>
                  <button
                    onClick={() => handleAction('clear_cache')}
                    disabled={performanceActions.isPending}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 disabled:opacity-50"
                  >
                    <Database className="h-4 w-4 mr-2" />
                    Clear All Caches
                  </button>
                  <button
                    onClick={() => handleAction('cleanup_cache')}
                    disabled={performanceActions.isPending}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Cleanup Expired
                  </button>
                </div>
                <p className="mt-3 text-sm text-gray-500">
                  Use these actions to manage system performance and clear cached data.
                </p>
              </div>
          </div>
        )}

        {/* Slowest Operations */}
        {performanceData.details?.slowestOperations && performanceData.details.slowestOperations.length > 0 && (
          <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  <Server className="inline h-5 w-5 mr-2" />
                  Slowest Operations
                </h3>
                <div className="overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Operation
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Duration
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Timestamp
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {performanceData.details.slowestOperations.slice(0, 10).map((operation, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {operation.operation}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {operation.duration.toFixed(0)}ms
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(operation.timestamp).toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
            </div>
          </div>
        )}
      </>
    )}
      </div>
    </AdminLayout>
  );
}

export default function PerformancePageWithErrorBoundary() {
  return (
    <ErrorBoundary>
      <PerformancePage />
    </ErrorBoundary>
  );
}
