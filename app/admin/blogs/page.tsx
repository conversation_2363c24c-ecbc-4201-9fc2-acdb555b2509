'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { BlogPost } from '@/types/blog';
import AdminLayout from '@/components/layout/AdminLayout';
import { Edit, Trash2, Plus } from 'lucide-react';
import { useBlogs, useDeleteBlog, useToggleBlogStatus, BlogQueryParams } from '@/hooks/useBlogs';
import { handleClientError } from '@/lib/error-handler';
import { useToast } from '@/hooks/useToast';
import { useDebounce } from '@/hooks/useDebounce';
import { PageErrorBoundary, SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';

export default function BlogManagementPage() {
  const router = useRouter();
  const toast = useToast();

  // Filters and pagination state
  const [search, setSearch] = useState('');
  const [category, setCategory] = useState('all');
  const [isPublished, setIsPublished] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
  });

  // Delete modal state
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [blogToDelete, setBlogToDelete] = useState<BlogPost | null>(null);

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(search, 300);

  // Use React Query for data fetching with debounced search
  const queryParams: BlogQueryParams = {
    page: pagination.page,
    limit: pagination.limit,
    ...(debouncedSearch && { search: debouncedSearch }),
    ...(category !== 'all' && { category }),
    ...(isPublished !== null && { isPublished }),
  };

  const {
    data: blogsData,
    isLoading: loading,
    error: queryError,
    refetch,
  } = useBlogs(queryParams);

  const deleteBlogMutation = useDeleteBlog();
  const toggleStatusMutation = useToggleBlogStatus();

  const blogs = blogsData?.data || [];
  const paginationData = blogsData?.pagination || { page: 1, limit: 10, total: 0, totalPages: 0 };
  const error = queryError ? handleClientError(queryError, 'fetching blogs') : null;

  // Handle search with debouncing
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on search
  };

  const handleCategoryChange = (value: string) => {
    setCategory(value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on filter change
  };

  const handleStatusChange = (value: string) => {
    setIsPublished(value === '' ? null : value);
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on filter change
  };

  const handleDelete = (blog: BlogPost) => {
    setBlogToDelete(blog);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!blogToDelete) return;

    try {
      await deleteBlogMutation.mutateAsync(blogToDelete.id);
      setShowDeleteModal(false);
      setBlogToDelete(null);
      // Success and error toasts are handled by the hook
    } catch (error) {
      console.error('Error deleting blog:', error);
      // Error toast is handled by the hook
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setBlogToDelete(null);
  };

  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    const toastId = toast.loading(currentStatus ? 'Unpublishing blog...' : 'Publishing blog...');

    try {
      await toggleStatusMutation.mutateAsync({ id, isPublished: !currentStatus });
      toast.success(currentStatus ? 'Blog unpublished successfully' : 'Blog published successfully');
    } catch (error) {
      console.error('Error updating blog status:', error);
      const errorMessage = handleClientError(error, 'updating blog status');
      toast.error(errorMessage);
    } finally {
      toast.dismiss(toastId);
    }
  };

  return (
    <PageErrorBoundary context="admin-blogs-page">
      <AdminLayout>
        <SectionErrorBoundary context="admin-blogs-header">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Blog Management</h1>
            <Button
              onClick={() => router.push('/admin/blogs/new')}
              className="flex items-center"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add New Blog
            </Button>
          </div>
        </SectionErrorBoundary>

        {/* Filters */}
        <SectionErrorBoundary context="admin-blogs-filters">
          <div className="bg-white p-4 rounded-lg shadow mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  value={search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  placeholder="Search blogs..."
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={category}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="all">All Categories</option>
                  {/* Add categories dynamically if needed */}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={isPublished === null ? '' : isPublished}
                  onChange={(e) => handleStatusChange(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                >
                  <option value="">All</option>
                  <option value="true">Published</option>
                  <option value="false">Draft</option>
                </select>
              </div>
            </div>
          </div>
        </SectionErrorBoundary>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

        {/* Blogs table */}
        <SectionErrorBoundary context="admin-blogs-table">
          <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Author
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Published
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center">
                  <LoadingSpinner size="medium" />
                </td>
              </tr>
            ) : blogs.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                  No blog posts found
                </td>
              </tr>
            ) : (
              blogs.map((blog) => (
                <tr key={blog.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {blog.title}
                    </div>
                    {blog.excerpt && (
                      <div className="text-sm text-gray-500 truncate max-w-md">
                        {blog.excerpt}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {blog.author}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">
                      {blog.category || 'Uncategorized'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => handleToggleStatus(blog.id, blog.is_published || false)}
                      className="flex items-center focus:outline-none"
                      aria-pressed={blog.is_published || false}
                      title={blog.is_published ? "Unpublish" : "Publish"}
                      disabled={toggleStatusMutation.isPending}
                    >
                      <span
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${
                          blog.is_published ? 'bg-green-500' : 'bg-gray-300'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${
                            blog.is_published ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </span>
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {blog.published_at
                      ? new Date(blog.published_at).toLocaleDateString()
                      : 'Not published'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Link
                        href={`/admin/blogs/${blog.id}/edit`}
                        className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-gray-100"
                        title="Edit Blog"
                      >
                        <Edit className="h-5 w-5" />
                      </Link>
                      <button
                        onClick={() => handleDelete(blog)}
                        className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-gray-100"
                        title="Delete Blog"
                        disabled={deleteBlogMutation.isPending}
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
          </div>
        </SectionErrorBoundary>

      {/* Pagination */}
      {!loading && paginationData.totalPages > 1 && (
        <div className="flex justify-between items-center mt-6">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{(paginationData.page - 1) * paginationData.limit + 1}</span> to{' '}
            <span className="font-medium">
              {Math.min(paginationData.page * paginationData.limit, paginationData.total)}
            </span>{' '}
            of <span className="font-medium">{paginationData.total}</span> results
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setPagination((prev) => ({ ...prev, page: prev.page - 1 }))}
              disabled={paginationData.page === 1 || loading}
              className="px-3 py-1 border rounded-md disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setPagination((prev) => ({ ...prev, page: prev.page + 1 }))}
              disabled={paginationData.page === paginationData.totalPages || loading}
              className="px-3 py-1 border rounded-md disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {blogToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          title="Delete Blog Post"
          message={`Are you sure you want to delete "${blogToDelete.title}"?\n\nThis action cannot be undone and will permanently remove the blog post from the website.`}
          confirmText="Delete Blog"
          variant="danger"
          loading={deleteBlogMutation.isPending}
        />
      )}
      </AdminLayout>
    </PageErrorBoundary>
  );
}