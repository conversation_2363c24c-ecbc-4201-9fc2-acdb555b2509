import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { measureDatabaseQuery } from '@/lib/performance-monitor';

import { createSafeSearchCondition } from '@/lib/input-sanitization';
import { handleApiError } from '@/lib/error-handler';
import type { TripFilters, CreateTripData, TripDifficulty } from '@/types/database';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/trips - Get all trips with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters with validation
    const pageParam = searchParams.get('page') || '1';
    const limitParam = searchParams.get('limit') || '10';
    const page = Math.max(1, parseInt(pageParam, 10) || 1);
    const limit = Math.max(1, Math.min(50, parseInt(limitParam, 10) || 10));
    const search = searchParams.get('search');
    const difficulty = searchParams.get('difficulty');
    const destination = searchParams.get('destination');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');

    // Use performance monitoring without caching for real-time updates
    const result = await measureDatabaseQuery(
      'public_trips_list',
      async () => {
        const supabase = createServerSupabase();

            // Build query
            let query = supabase
              .from('trips')
              .select(`
                id,
                title,
                slug,
                description,
                destination,
                days,
                nights,
                price_per_person,
                difficulty,
                featured_image_url,
                is_featured,
                is_active
              `, { count: 'exact' })
              .eq('is_active', true);

            // Apply filters with safe search
            if (search) {
              const searchConditions = createSafeSearchCondition(search, ['title', 'description', 'destination']);
              if (searchConditions) {
                query = query.or(searchConditions);
              }
            }
            if (difficulty && difficulty !== 'all') {
              // Validate that difficulty is one of the allowed values
              const validDifficulties: TripDifficulty[] = ['easy', 'moderate', 'challenging', 'extreme'];
              if (validDifficulties.includes(difficulty as TripDifficulty)) {
                query = query.eq('difficulty', difficulty as TripDifficulty);
              }
            }
            if (destination) {
              query = query.ilike('destination', `%${destination}%`);
            }
            if (minPrice) {
              const minPriceNum = parseInt(minPrice, 10);
              if (!isNaN(minPriceNum) && minPriceNum >= 0) {
                query = query.gte('price_per_person', minPriceNum);
              }
            }
            if (maxPrice) {
              const maxPriceNum = parseInt(maxPrice, 10);
              if (!isNaN(maxPriceNum) && maxPriceNum >= 0) {
                query = query.lte('price_per_person', maxPriceNum);
              }
            }

            // Apply pagination
            const from = (page - 1) * limit;
            const to = from + limit - 1;

            query = query
              .order('is_featured', { ascending: false })
              .order('created_at', { ascending: false })
              .range(from, to);

            const { data: trips, error, count } = await query;

            if (error) {
              throw error;
            }

            const totalPages = Math.ceil((count || 0) / limit);

        return {
          data: trips || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages,
          },
        };
      },
      'public_query'
    );

    return NextResponse.json(result, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
      },
    });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// POST /api/trips - Create a new trip (Admin only)
export async function POST(request: NextRequest) {
  try {
    // For now, return placeholder response
    return NextResponse.json({
      message: 'Trip creation functionality temporarily disabled',
      data: null
    }, { status: 501 });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
