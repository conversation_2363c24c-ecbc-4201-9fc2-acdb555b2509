import { NextRequest, NextResponse } from 'next/server';
import { generateAuthUrl } from '@/lib/google-photos-auth';

export const dynamic = 'force-dynamic';

/**
 * Initiate Google Photos OAuth2 authorization flow
 * GET /api/auth/google?albumId=xxx (or legacy tripPhotoDetailsId=xxx)
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('[GOOGLE_PHOTOS_AUTH] ==> Starting OAuth initiation request');
    console.log('[GOOGLE_PHOTOS_AUTH] Request URL:', request.url);
    console.log('[GOOGLE_PHOTOS_AUTH] Request method:', request.method);
    console.log('[GOOGLE_PHOTOS_AUTH] Request headers:', Object.fromEntries(request.headers.entries()));

    const { searchParams } = new URL(request.url);
    // Support both new albumId and legacy tripPhotoDetailsId
    const albumId = searchParams.get('albumId') || searchParams.get('tripPhotoDetailsId');

    console.log('[GOOGLE_PHOTOS_AUTH] Extracted albumId:', albumId);
    console.log('[GOOGLE_PHOTOS_AUTH] All search params:', Object.fromEntries(searchParams.entries()));

    if (!albumId) {
      console.error('[GOOGLE_PHOTOS_AUTH] ❌ Missing albumId parameter');
      return NextResponse.json(
        { error: 'Missing albumId parameter' },
        { status: 400 }
      );
    }

    console.log(`[GOOGLE_PHOTOS_AUTH] ✅ Valid albumId received: ${albumId}`);
    console.log(`[GOOGLE_PHOTOS_AUTH] Initiating OAuth flow for album: ${albumId}`);

    // Check environment variables
    console.log('[GOOGLE_PHOTOS_AUTH] Checking environment variables...');
    console.log('[GOOGLE_PHOTOS_AUTH] GOOGLE_PHOTOS_CLIENT_ID exists:', !!process.env.GOOGLE_PHOTOS_CLIENT_ID);
    console.log('[GOOGLE_PHOTOS_AUTH] GOOGLE_PHOTOS_CLIENT_SECRET exists:', !!process.env.GOOGLE_PHOTOS_CLIENT_SECRET);
    console.log('[GOOGLE_PHOTOS_AUTH] GOOGLE_PHOTOS_REDIRECT_URI:', process.env.GOOGLE_PHOTOS_REDIRECT_URI);

    // Generate authorization URL
    console.log('[GOOGLE_PHOTOS_AUTH] Generating authorization URL...');
    const authUrl = generateAuthUrl(albumId);
    console.log(`[GOOGLE_PHOTOS_AUTH] ✅ Generated auth URL: ${authUrl}`);

    const duration = Date.now() - startTime;
    console.log(`[GOOGLE_PHOTOS_AUTH] ✅ OAuth initiation completed in ${duration}ms`);
    console.log(`[GOOGLE_PHOTOS_AUTH] Redirecting to Google authorization server...`);

    // Redirect to Google's authorization server
    return NextResponse.redirect(authUrl);
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[GOOGLE_PHOTOS_AUTH] ❌ Error initiating OAuth flow after ${duration}ms:`, error);
    console.error('[GOOGLE_PHOTOS_AUTH] Error stack:', error instanceof Error ? error.stack : 'No stack trace');

    return NextResponse.json(
      {
        error: 'Failed to initiate Google Photos authorization',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
