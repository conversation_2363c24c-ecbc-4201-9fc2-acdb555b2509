import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { measureDatabaseQuery } from '@/lib/performance-monitor';
import { handleApiError } from '@/lib/error-handler';

// GET /api/galleries - Get all active galleries with images for public display
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters with validation
    const gallery_id = searchParams.get('gallery_id');
    const category = searchParams.get('category');
    const limitParam = searchParams.get('limit') || '50';

    // Graceful handling of non-numeric parameters
    const parsedLimit = parseInt(limitParam, 10);
    const limit = Math.max(1, Math.min(100, isNaN(parsedLimit) ? 50 : parsedLimit));

    if (gallery_id) {
      // Use performance monitoring without caching for real-time updates
      try {
        const result = await measureDatabaseQuery(
          'public_gallery_single',
          async () => {
            const supabase = createServerSupabase();

                // Get specific gallery with images
                const { data: gallery, error } = await supabase
                  .from('galleries')
                  .select(`
                    *,
                    trips:trip_id (
                      id,
                      title,
                      destination,
                      category
                    ),
                    gallery_images (
                      id,
                      image_url,
                      order_index
                    )
                  `)
                  .eq('id', gallery_id)
                  .eq('is_active', true)
                  .single();

                if (error) {
                  if (error.code === 'PGRST116') {
                    throw new Error('Gallery not found or has been deactivated');
                  }
                  throw error;
                }

                // Sort images by order_index
                if (gallery.gallery_images) {
                  gallery.gallery_images.sort((a, b) => (a.order_index || 0) - (b.order_index || 0));
                }

            return gallery;
          },
          'public_query'
        );

        return NextResponse.json(result, {
          headers: {
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Surrogate-Control': 'no-store',
          },
        });
      } catch (galleryError: unknown) {
        // Handle gallery-specific errors (like not found)
        const errorMessage = galleryError instanceof Error ? galleryError.message : 'Unknown error';
        if (errorMessage.includes('Gallery not found')) {
          return NextResponse.json(
            { error: errorMessage },
            {
              status: 404,
              headers: {
                'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
                'Pragma': 'no-cache',
                'Expires': '0',
                'Surrogate-Control': 'no-store',
              }
            }
          );
        }
        throw galleryError; // Re-throw other errors to be handled by main catch block
      }
    } else {
      // Use performance monitoring without caching for real-time updates
      const result = await measureDatabaseQuery(
        'public_galleries_list',
        async () => {
          const supabase = createServerSupabase();

              // Get all active galleries with their images for the gallery page
              let query = supabase
                .from('galleries')
                .select(`
                  id,
                  name,
                  description,
                  trip_id,
                  folder_name,
                  is_active,
                  featured_image_url,
                  created_at,
                  updated_at,
                  trips:trip_id (
                    id,
                    title,
                    destination,
                    category
                  ),
                  gallery_images (
                    id,
                    gallery_id,
                    image_url,
                    cloudinary_public_id,
                    order_index,
                    created_at,
                    updated_at
                  )
                `)
                .eq('is_active', true);

              // Apply category filter if provided
              if (category && category !== 'all') {
                query = query.eq('trips.category', category);
              }

              query = query
                .order('created_at', { ascending: false })
                .limit(limit);

              const { data: galleries, error } = await query;

              if (error) {
                throw error;
              }

              // Transform galleries into the format expected by the gallery page
              const galleryImages = [];

              for (const gallery of galleries || []) {
                if (gallery.gallery_images && gallery.gallery_images.length > 0) {
                  // Sort images by order_index
                  const sortedImages = gallery.gallery_images.sort(
                    (a, b) => (a.order_index || 0) - (b.order_index || 0)
                  );

                  for (const image of sortedImages) {
                    galleryImages.push({
                      id: image.id,
                      title: gallery.name,
                      category: gallery.trips?.category || 'General',
                      location: gallery.trips?.destination || 'Various Locations',
                      imageUrl: image.image_url,
                      description: gallery.description || '',
                      gallery_name: gallery.name,
                      gallery_id: gallery.id
                    });
                  }
                } else if (gallery.featured_image_url) {
                  // If no images in gallery_images but has featured image, include it
                  galleryImages.push({
                    id: `featured-${gallery.id}`,
                    title: gallery.name,
                    category: gallery.trips?.category || 'General',
                    location: gallery.trips?.destination || 'Various Locations',
                    imageUrl: gallery.featured_image_url,
                    description: gallery.description || '',
                    gallery_name: gallery.name,
                    gallery_id: gallery.id
                  });
                }
              }

          return {
            data: galleryImages,
            galleries: galleries || []
          };
        },
        'public_query'
      );

      return NextResponse.json(result, {
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
          'Pragma': 'no-cache',
          'Expires': '0',
          'Surrogate-Control': 'no-store',
        },
      });
    }
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
