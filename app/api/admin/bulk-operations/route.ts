import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { logAdminAccess, getRequestContext } from '@/lib/audit-logger';
import { measureDatabaseQuery } from '@/lib/performance-monitor';
import { handleApiError } from '@/lib/error-handler';
import { SupabaseClient } from '@supabase/supabase-js';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Supported bulk operations
type BulkOperation =
  | 'delete_trips'
  | 'update_trip_status'
  | 'delete_blog_posts'
  | 'update_blog_status'
  | 'delete_inquiries'
  | 'update_inquiry_status'
  | 'export_data';

// Allowed tables for export (security measure)
const ALLOWED_EXPORT_TABLES = ['trips', 'blog_posts', 'inquiries', 'galleries'] as const;
type AllowedTable = typeof ALLOWED_EXPORT_TABLES[number];

interface BulkOperationRequest {
  operation: BulkOperation;
  ids: string[];
  data?: Record<string, any>;
  filters?: Record<string, any>;
  table?: AllowedTable; // For export operations
}

// POST /api/admin/bulk-operations - Perform bulk operations on content
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin authentication and permissions
    const { user, hasAccess } = await verifyAdminAccess('trips', 'delete'); // Using trips permission as proxy

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required for bulk operations.' },
        { status: 403 }
      );
    }

    const body: BulkOperationRequest = await request.json();
    const { operation, ids, data, filters, table } = body;
    const requestContext = getRequestContext(request);

    // Validate request
    if (!operation) {
      return NextResponse.json(
        { error: 'Operation is required' },
        { status: 400 }
      );
    }

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'IDs array is required and must not be empty' },
        { status: 400 }
      );
    }

    if (ids.length > 100) {
      return NextResponse.json(
        { error: 'Maximum 100 items can be processed in a single bulk operation' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();
    let result: any = { success: true, operation, processed: 0, errors: [] };

    switch (operation) {
      case 'delete_trips':
        result = await measureDatabaseQuery(
          `bulk_delete_trips_${ids.length}`,
          () => performBulkDeleteTrips(supabase, ids),
          'bulk_delete'
        );
        // Invalidate trips cache
        break;

      case 'update_trip_status':
        if (!data || data.is_active === undefined) {
          return NextResponse.json(
            { error: 'is_active status is required for trip status update' },
            { status: 400 }
          );
        }
        result = await measureDatabaseQuery(
          `bulk_update_trip_status_${ids.length}`,
          () => performBulkUpdateTripStatus(supabase, ids, data.is_active),
          'bulk_update'
        );
        break;

      case 'delete_blog_posts':
        result = await measureDatabaseQuery(
          `bulk_delete_blog_posts_${ids.length}`,
          () => performBulkDeleteBlogPosts(supabase, ids),
          'bulk_delete'
        );
        break;

      case 'update_blog_status':
        if (!data || data.is_published === undefined) {
          return NextResponse.json(
            { error: 'is_published status is required for blog status update' },
            { status: 400 }
          );
        }
        result = await measureDatabaseQuery(
          `bulk_update_blog_status_${ids.length}`,
          () => performBulkUpdateBlogStatus(supabase, ids, data.is_published),
          'bulk_update'
        );
        break;

      case 'delete_inquiries':
        result = await measureDatabaseQuery(
          `bulk_delete_inquiries_${ids.length}`,
          () => performBulkDeleteInquiries(supabase, ids),
          'bulk_delete'
        );
        // Invalidate admin dashboard cache for inquiries
        break;

      case 'update_inquiry_status':
        if (!data || !data.status) {
          return NextResponse.json(
            { error: 'Status is required for inquiry status update' },
            { status: 400 }
          );
        }
        result = await measureDatabaseQuery(
          `bulk_update_inquiry_status_${ids.length}`,
          () => performBulkUpdateInquiryStatus(supabase, ids, data.status),
          'bulk_update'
        );
        // Invalidate admin dashboard cache for inquiries
        break;

      case 'export_data': {
        // Validate table parameter for security - use correct source
        const exportTable = table || 'trips';
        if (!ALLOWED_EXPORT_TABLES.includes(exportTable as AllowedTable)) {
          return NextResponse.json(
            { error: `Invalid table for export: ${exportTable}. Allowed tables: ${ALLOWED_EXPORT_TABLES.join(', ')}` },
            { status: 400 }
          );
        }
        result = await measureDatabaseQuery(
          `bulk_export_data_${ids.length}`,
          () => performBulkExportData(supabase, ids, exportTable as AllowedTable),
          'bulk_export'
        );
        break;
      }

      default:
        return NextResponse.json(
          { error: `Unsupported operation: ${operation}` },
          { status: 400 }
        );
    }

    // Log the bulk operation
    await logAdminAccess(
      user.id,
      user.username || 'Unknown',
      'bulk_operations',
      `bulk operation: ${operation}`,
      {
        ...requestContext,
        metadata: {
          operation,
          items_count: ids.length,
          processed: result.processed,
          errors_count: result.errors?.length || 0,
          data_provided: !!data,
        },
      }
    );

    return NextResponse.json(result);

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// Bulk operation implementations
async function performBulkDeleteTrips(supabase: SupabaseClient, ids: string[]) {
  const result = { success: true, operation: 'delete_trips', processed: 0, errors: [] as any[] };

  try {
    // Use batch delete instead of row-by-row for better performance
    const { data, error } = await supabase
      .from('trips')
      .delete()
      .in('id', ids)
      .select('id');

    if (error) {
      result.errors.push({ error: error.message });
    } else {
      result.processed = data?.length || 0;
    }
  } catch (error) {
    result.errors.push({ error: error instanceof Error ? error.message : 'Unknown error' });
  }

  return result;
}

async function performBulkUpdateTripStatus(supabase: SupabaseClient, ids: string[], isActive: boolean) {
  const result = { success: true, operation: 'update_trip_status', processed: 0, errors: [] as any[] };

  try {
    const { data, error } = await supabase
      .from('trips')
      .update({ is_active: isActive, updated_at: new Date().toISOString() })
      .in('id', ids)
      .select('id');

    if (error) {
      result.errors.push({ error: error.message });
    } else {
      result.processed = data?.length || 0;
    }
  } catch (error) {
    result.errors.push({ error: error instanceof Error ? error.message : 'Unknown error' });
  }

  return result;
}

async function performBulkDeleteBlogPosts(supabase: SupabaseClient, ids: string[]) {
  const result = { success: true, operation: 'delete_blog_posts', processed: 0, errors: [] as any[] };

  try {
    // Use batch delete instead of row-by-row for better performance
    const { data, error } = await supabase
      .from('blog_posts')
      .delete()
      .in('id', ids)
      .select('id');

    if (error) {
      result.errors.push({ error: error.message });
    } else {
      result.processed = data?.length || 0;
    }
  } catch (error) {
    result.errors.push({ error: error instanceof Error ? error.message : 'Unknown error' });
  }

  return result;
}

async function performBulkUpdateBlogStatus(supabase: SupabaseClient, ids: string[], isPublished: boolean) {
  const result = { success: true, operation: 'update_blog_status', processed: 0, errors: [] as any[] };

  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .update({ is_published: isPublished, updated_at: new Date().toISOString() })
      .in('id', ids)
      .select('id');

    if (error) {
      result.errors.push({ error: error.message });
    } else {
      result.processed = data?.length || 0;
    }
  } catch (error) {
    result.errors.push({ error: error instanceof Error ? error.message : 'Unknown error' });
  }

  return result;
}

async function performBulkDeleteInquiries(supabase: SupabaseClient, ids: string[]) {
  const result = { success: true, operation: 'delete_inquiries', processed: 0, errors: [] as any[] };

  try {
    // Use batch delete instead of row-by-row for better performance
    const { data, error } = await supabase
      .from('inquiries')
      .delete()
      .in('id', ids)
      .select('id');

    if (error) {
      result.errors.push({ error: error.message });
    } else {
      result.processed = data?.length || 0;
    }
  } catch (error) {
    result.errors.push({ error: error instanceof Error ? error.message : 'Unknown error' });
  }

  return result;
}

async function performBulkUpdateInquiryStatus(supabase: SupabaseClient, ids: string[], status: string) {
  const result = { success: true, operation: 'update_inquiry_status', processed: 0, errors: [] as any[] };

  try {
    const { data, error } = await supabase
      .from('inquiries')
      .update({ status, updated_at: new Date().toISOString() })
      .in('id', ids)
      .select('id');

    if (error) {
      result.errors.push({ error: error.message });
    } else {
      result.processed = data?.length || 0;
    }
  } catch (error) {
    result.errors.push({ error: error instanceof Error ? error.message : 'Unknown error' });
  }

  return result;
}

async function performBulkExportData(supabase: SupabaseClient, ids: string[], table: AllowedTable) {
  const result = { success: true, operation: 'export_data', processed: 0, errors: [] as any[], data: [] as any[] };

  try {
    // Validate table is in allowed list (additional security check)
    if (!ALLOWED_EXPORT_TABLES.includes(table)) {
      result.errors.push({ error: `Table '${table}' is not allowed for export` });
      return result;
    }

    const { data, error } = await supabase
      .from(table)
      .select('*')
      .in('id', ids);

    if (error) {
      result.errors.push({ error: error.message });
    } else {
      result.data = data || [];
      result.processed = data?.length || 0;
    }
  } catch (error) {
    result.errors.push({ error: error instanceof Error ? error.message : 'Unknown error' });
  }

  return result;
}
