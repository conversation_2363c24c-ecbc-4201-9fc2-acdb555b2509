import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';

interface DashboardStats {
  totalTrips: number;
  activeTrips: number;
  draftTrips: number;
  totalBlogs: number;
  publishedBlogs: number;
  draftBlogs: number;
  totalInquiries: number;
  newInquiries: number;
  respondedInquiries: number;
  totalPhotos: number;
  recentTrips: Array<{
    id: string;
    title: string;
    slug: string;
    destination: string;
    created_at: string | null;
    is_active: boolean | null;
  }>;
  recentBlogs: Array<{
    id: string;
    title: string;
    slug: string;
    created_at: string | null;
    is_published: boolean | null;
  }>;
  recentInquiries: Array<{
    id: string;
    name: string;
    email: string;
    subject: string | null;
    status: string | null;
    created_at: string | null;
  }>;
}

export async function GET(_request: NextRequest) {
  try {
    // Verify admin authentication - any admin can access dashboard
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }


    const supabase = createAdminClient();

    // Initialize stats object
    const stats: DashboardStats = {
      totalTrips: 0,
      activeTrips: 0,
      draftTrips: 0,
      totalBlogs: 0,
      publishedBlogs: 0,
      draftBlogs: 0,
      totalInquiries: 0,
      newInquiries: 0,
      respondedInquiries: 0,
      totalPhotos: 0,
      recentTrips: [],
      recentBlogs: [],
      recentInquiries: []
    };

    // Fetch trips data
    try {
      const [tripsResult, activeTripsResult, draftTripsResult, recentTripsResult] = await Promise.all([
        supabase.from('trips').select('id', { count: 'exact', head: true }),
        supabase.from('trips').select('id', { count: 'exact', head: true }).eq('is_active', true),
        supabase.from('trips').select('id', { count: 'exact', head: true }).eq('is_active', false),
        supabase
          .from('trips')
          .select('id, title, slug, destination, created_at, is_active')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      stats.totalTrips = tripsResult.count || 0;
      stats.activeTrips = activeTripsResult.count || 0;
      stats.draftTrips = draftTripsResult.count || 0;
      stats.recentTrips = recentTripsResult.data || [];
    } catch (error) {
      console.error('Error fetching trips data:', error);
    }

    // Fetch blogs data
    try {
      const [blogsResult, publishedBlogsResult, draftBlogsResult, recentBlogsResult] = await Promise.all([
        supabase.from('blog_posts').select('id', { count: 'exact', head: true }),
        supabase.from('blog_posts').select('id', { count: 'exact', head: true }).eq('is_published', true),
        supabase.from('blog_posts').select('id', { count: 'exact', head: true }).eq('is_published', false),
        supabase
          .from('blog_posts')
          .select('id, title, slug, created_at, is_published')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      stats.totalBlogs = blogsResult.count || 0;
      stats.publishedBlogs = publishedBlogsResult.count || 0;
      stats.draftBlogs = draftBlogsResult.count || 0;
      stats.recentBlogs = recentBlogsResult.data || [];
    } catch (error) {
      console.error('Error fetching blogs data:', error);
    }

    // Fetch inquiries data
    try {
      const [inquiriesResult, newInquiriesResult, respondedInquiriesResult, recentInquiriesResult] = await Promise.all([
        supabase.from('inquiries').select('id', { count: 'exact', head: true }),
        supabase.from('inquiries').select('id', { count: 'exact', head: true }).eq('status', 'new'),
        supabase.from('inquiries').select('id', { count: 'exact', head: true }).eq('status', 'resolved'),
        supabase
          .from('inquiries')
          .select('id, name, email, subject, status, created_at')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      stats.totalInquiries = inquiriesResult.count || 0;
      stats.newInquiries = newInquiriesResult.count || 0;
      stats.respondedInquiries = respondedInquiriesResult.count || 0;
      stats.recentInquiries = recentInquiriesResult.data || [];
    } catch (error) {
      console.error('Error fetching inquiries data:', error);
    }

    // Fetch photos data
    try {
      const photosResult = await supabase
        .from('trip_photos_details')
        .select('id', { count: 'exact', head: true });

      stats.totalPhotos = photosResult.count || 0;
    } catch (error) {
      console.error('Error fetching photos data:', error);
    }

    // Dashboard access is a read operation - no audit logging needed

    return NextResponse.json({ stats }, {
      headers: {
        'Cache-Control': 'private, no-cache, no-store, must-revalidate',
      },
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
