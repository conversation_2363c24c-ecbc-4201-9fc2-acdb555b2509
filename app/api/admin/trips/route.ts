import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { createSafeSearchCondition } from '@/lib/input-sanitization';
import { logTripCrud, crudAuditLogger } from '@/lib/crud-audit-logger';
import { handleApiError } from '@/lib/error-handler';

// GET /api/admin/trips - Get all trips with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const destination = searchParams.get('destination');
    const isActive = searchParams.get('isActive');

    // Build query
    let query = supabase
      .from('trips')
      .select('*', { count: 'exact' });

    // Apply filters with safe search
    if (search) {
      const searchConditions = createSafeSearchCondition(search, ['title', 'description']);
      if (searchConditions) {
        query = query.or(searchConditions);
      }
    }
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }
    if (destination && destination !== 'all') {
      query = query.eq('destination', destination);
    }
    if (isActive !== null && isActive !== 'all') {
      query = query.eq('is_active', isActive === 'true');
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: trips, error, count } = await query;

    if (error) {
      console.error('Error fetching trips:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trips' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: trips || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// POST /api/admin/trips - Create a new trip
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Basic validation
    const requiredFields = ['title', 'description', 'destination', 'days', 'nights', 'price_per_person', 'difficulty'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate nights = days - 1 with proper type coercion and NaN checks
    const days = Number(body.days);
    const nights = Number(body.nights);

    // Check for NaN values before comparison
    if (isNaN(days) || isNaN(nights)) {
      return NextResponse.json(
        { error: 'Days and nights must be valid numbers' },
        { status: 400 }
      );
    }

    const expectedNights = Math.max(0, days - 1);

    if (nights !== expectedNights) {
      return NextResponse.json(
        { error: `Nights must be exactly ${expectedNights} for ${days} day${days > 1 ? 's' : ''}` },
        { status: 400 }
      );
    }

    // Generate slug from title
    const slug = body.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Save trip to database
    const { data: trip, error: dbError } = await supabase
      .from('trips')
      .insert({
        ...body,
        slug,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving trip to database:', dbError);

      // Log failed creation
      await crudAuditLogger.logFailedOperation(
        'create',
        'trip',
        'unknown',
        user.id,
        user.username || 'Unknown',
        dbError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to save trip' },
        { status: 500 }
      );
    }

    // Log successful creation
    await logTripCrud.create(
      trip.id,
      trip.title,
      user.id,
      user.username || 'Unknown',
      body,
      request
    );

    return NextResponse.json({ data: trip });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
} 