import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { crud<PERSON>uditLogger, CrudAuditLogger } from '@/lib/crud-audit-logger';
import { deleteFromCloudinary, extractCloudinaryPublicId } from '@/lib/cloudinary';
import { handleApiError } from '@/lib/error-handler';

// GET /api/admin/team-members/[id] - Get single team member (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id: teamMemberId } = await params;

    if (!teamMemberId) {
      return NextResponse.json(
        { error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminClient();

    const { data: teamMember, error } = await adminSupabase
      .from('team_members')
      .select('*')
      .eq('id', teamMemberId)
      .single();

    if (error) {
      console.error('Error fetching team member:', error);
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ teamMember });

  } catch (error: any) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// PUT /api/admin/team-members/[id] - Update team member (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id: teamMemberId } = await params;

    if (!teamMemberId) {
      return NextResponse.json(
        { error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, position, bio, image_url, sort_order, is_active } = body;

    // For partial updates, only validate fields that are being updated
    // Don't require all fields for legitimate partial updates
    if (name !== undefined && !name.trim()) {
      return NextResponse.json(
        { error: 'Name cannot be empty' },
        { status: 400 }
      );
    }
    if (position !== undefined && !position.trim()) {
      return NextResponse.json(
        { error: 'Position cannot be empty' },
        { status: 400 }
      );
    }
    if (bio !== undefined && !bio.trim()) {
      return NextResponse.json(
        { error: 'Bio cannot be empty' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminClient();

    // Get original team member data for change tracking
    const { data: originalTeamMember } = await adminSupabase
      .from('team_members')
      .select('*')
      .eq('id', teamMemberId)
      .single();

    // Store old image URL for cleanup after successful DB update
    let oldImageToDelete: string | null = null;
    if (image_url !== undefined && originalTeamMember?.image_url &&
        image_url !== originalTeamMember.image_url) {
      oldImageToDelete = originalTeamMember.image_url;
    }

    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    // Only update fields that are provided (partial updates)
    if (name !== undefined) updateData.name = name;
    if (position !== undefined) updateData.position = position;
    if (bio !== undefined) updateData.bio = bio;
    if (image_url !== undefined) updateData.image_url = image_url;
    if (sort_order !== undefined) updateData.sort_order = sort_order;
    if (is_active !== undefined) updateData.is_active = is_active;

    const { data: teamMember, error } = await adminSupabase
      .from('team_members')
      .update(updateData)
      .eq('id', teamMemberId)
      .select()
      .single();

    if (error) {
      console.error('Error updating team member:', error);

      // Log failed update (wrap in try/catch)
      try {
        await crudAuditLogger.logFailedOperation(
          'update',
          'team_member',
          teamMemberId,
          user.id,
          user.username || 'Unknown',
          error.message,
          request
        );
      } catch (auditError) {
        console.error('Failed to log audit entry:', auditError);
      }

      return NextResponse.json(
        { error: 'Failed to update team member' },
        { status: 500 }
      );
    }

    // Track changes for audit log using utility function
    const changes = originalTeamMember ? CrudAuditLogger.calculateChanges(originalTeamMember, updateData) : {};

    // Log successful update (wrap in try/catch to avoid breaking the happy-path)
    try {
      await crudAuditLogger.logCrudOperation({
        operation: 'update',
        resourceType: 'team_member',
        resourceId: teamMemberId,
        resourceTitle: teamMember.name,
        userId: user.id,
        userEmail: user.username || 'Unknown',
        changes,
        success: true,
        metadata: {
          updated_at: new Date().toISOString(),
          fields_changed: Object.keys(changes),
        },
      }, request);
    } catch (auditError) {
      console.error('Failed to log audit entry:', auditError);
      // Continue with the response - don't fail the operation due to audit logging issues
    }

    // Clean up old image from Cloudinary after successful DB update (non-blocking)
    if (oldImageToDelete) {
      const oldPublicId = extractCloudinaryPublicId(oldImageToDelete);
      if (oldPublicId) {
        // Fire and forget - don't block the response
        deleteFromCloudinary(oldPublicId).catch(cloudinaryError => {
          console.error('Error deleting old team member image from Cloudinary:', cloudinaryError);
        });
      }
    }

    return NextResponse.json({
      message: 'Team member updated successfully',
      teamMember,
    });

  } catch (error: any) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// DELETE /api/admin/team-members/[id] - Delete team member (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id: teamMemberId } = await params;

    if (!teamMemberId) {
      return NextResponse.json(
        { error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminClient();

    // Get team member data before deletion for audit log and Cloudinary cleanup
    const { data: teamMemberToDelete } = await adminSupabase
      .from('team_members')
      .select('name, image_url')
      .eq('id', teamMemberId)
      .single();

    // Delete image from Cloudinary if it exists
    if (teamMemberToDelete?.image_url) {
      const publicId = extractCloudinaryPublicId(teamMemberToDelete.image_url);
      if (publicId) {
        try {
          await deleteFromCloudinary(publicId);
        } catch (cloudinaryError) {
          console.error('Error deleting team member image from Cloudinary:', cloudinaryError);
          // Continue with database deletion even if Cloudinary deletion fails
        }
      }
    }

    const { error } = await adminSupabase
      .from('team_members')
      .delete()
      .eq('id', teamMemberId);

    if (error) {
      console.error('Error deleting team member:', error);

      // Log failed deletion (wrap in try/catch)
      try {
        await crudAuditLogger.logFailedOperation(
          'delete',
          'team_member',
          teamMemberId,
          user.id,
          user.username || 'Unknown',
          error.message,
          request
        );
      } catch (auditError) {
        console.error('Failed to log audit entry:', auditError);
      }

      return NextResponse.json(
        { error: 'Failed to delete team member' },
        { status: 500 }
      );
    }

    // Log successful deletion (wrap in try/catch to avoid breaking the happy-path)
    try {
      await crudAuditLogger.logCrudOperation({
        operation: 'delete',
        resourceType: 'team_member',
        resourceId: teamMemberId,
        resourceTitle: teamMemberToDelete?.name || 'Unknown Team Member',
        userId: user.id,
        userEmail: user.username || 'Unknown',
        success: true,
        metadata: {
          deleted_at: new Date().toISOString(),
        },
      }, request);
    } catch (auditError) {
      console.error('Failed to log audit entry:', auditError);
      // Continue with the response - don't fail the operation due to audit logging issues
    }

    return NextResponse.json({
      message: 'Team member deleted successfully',
    });

  } catch (error: any) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
