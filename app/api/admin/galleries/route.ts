import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { verifyAdminAccess } from '@/lib/auth-server';
import { createSafeSearchCondition } from '@/lib/input-sanitization';

// GET /api/admin/galleries - Get all galleries with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters with validation
    const pageParam = searchParams.get('page') || '1';
    const limitParam = searchParams.get('limit') || '10';

    // Graceful handling of non-numeric parameters
    const parsedPage = parseInt(pageParam, 10);
    const parsedLimit = parseInt(limitParam, 10);

    const page = Math.max(1, isNaN(parsedPage) ? 1 : parsedPage);
    const limit = Math.max(1, Math.min(100, isNaN(parsedLimit) ? 10 : parsedLimit));
    const search = searchParams.get('search');
    const trip_id = searchParams.get('trip_id');
    const is_active = searchParams.get('is_active');

    // Build query
    let query = supabase
      .from('galleries')
      .select(`
        *,
        trips:trip_id (
          id,
          title,
          destination
        ),
        gallery_images (
          id
        )
      `, { count: 'exact' });

    // Apply filters with safe search
    if (search) {
      const searchConditions = createSafeSearchCondition(search, ['name', 'description']);
      if (searchConditions) {
        query = query.or(searchConditions);
      }
    }
    if (trip_id && trip_id !== 'all') {
      query = query.eq('trip_id', trip_id);
    }
    if (is_active !== null && is_active !== 'all') {
      query = query.eq('is_active', is_active === 'true');
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: galleries, error, count } = await query;

    if (error) {
      console.error('Error fetching galleries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch galleries' },
        { status: 500 }
      );
    }

    // Add image count to each gallery
    const galleriesWithImageCount = galleries?.map(gallery => ({
      ...gallery,
      image_count: gallery.gallery_images?.length || 0,
      gallery_images: undefined // Remove the images array to keep response clean
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: galleriesWithImageCount,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/galleries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/galleries - Create a new gallery
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createServerSupabase();
    const body = await request.json();

    // Enhanced input validation
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const requiredFields = ['name'];
    for (const field of requiredFields) {
      if (!body[field] || typeof body[field] !== 'string' || body[field].trim() === '') {
        return NextResponse.json(
          { error: `Missing or invalid required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate optional fields
    const allowedFields = ['name', 'description', 'featured_image_url', 'folder_name', 'trip_id', 'is_active'];
    const validatedBody: Record<string, any> = {};

    for (const [key, value] of Object.entries(body)) {
      if (allowedFields.includes(key)) {
        if (key === 'is_active' && typeof value !== 'boolean') {
          return NextResponse.json(
            { error: 'is_active must be a boolean' },
            { status: 400 }
          );
        }
        validatedBody[key] = value;
      }
    }

    // Generate folder name from gallery name if not provided
    if (!validatedBody.folder_name) {
      validatedBody.folder_name = validatedBody.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Save gallery to database
    const { data: gallery, error: dbError } = await supabase
      .from('galleries')
      .insert(validatedBody as any)
      .select()
      .single();

    if (dbError) {
      console.error('Error saving gallery to database:', dbError);
      return NextResponse.json(
        { error: 'Failed to save gallery' },
        { status: 500 }
      );
    }


    return NextResponse.json({ data: gallery });
  } catch (error) {
    console.error('Error in POST /api/admin/galleries:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
