import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';

// GET /api/admin/galleries/[id]/images - Get all images for a gallery
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    if (!id) {
      return NextResponse.json(
        { error: 'Gallery ID is required' },
        { status: 400 }
      );
    }

    const supabase = createAdminClient();
    const { searchParams } = new URL(request.url);

    // Parse query parameters with graceful handling of non-numeric values
    const pageParam = searchParams.get('page') || '1';
    const limitParam = searchParams.get('limit') || '50';
    const parsedPage = parseInt(pageParam, 10);
    const parsedLimit = parseInt(limitParam, 10);

    const page = Math.max(1, isNaN(parsedPage) ? 1 : parsedPage);
    const limit = Math.max(1, Math.min(100, isNaN(parsedLimit) ? 50 : parsedLimit));

    // Build query
    let query = supabase
      .from('gallery_images')
      .select('*', { count: 'exact' })
      .eq('gallery_id', id);

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('order_index', { ascending: true })
      .order('created_at', { ascending: true })
      .range(from, to);

    const { data: images, error, count } = await query;

    if (error) {
      console.error('Error fetching gallery images:', error);
      return NextResponse.json(
        { error: 'Failed to fetch gallery images' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: images || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/galleries/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/galleries/[id]/images - Add a new image to gallery
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Basic validation
    const requiredFields = ['image_url', 'cloudinary_public_id'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Verify gallery exists
    const { data: gallery, error: galleryError } = await supabase
      .from('galleries')
      .select('id')
      .eq('id', id)
      .single();

    if (galleryError || !gallery) {
      return NextResponse.json(
        { error: 'Gallery not found' },
        { status: 404 }
      );
    }

    // Get the next order index if not provided
    if (body.order_index === undefined) {
      const { data: lastImage } = await supabase
        .from('gallery_images')
        .select('order_index')
        .eq('gallery_id', id)
        .order('order_index', { ascending: false })
        .limit(1)
        .single();

      body.order_index = (lastImage?.order_index || 0) + 1;
    }

    // Save image to database
    const { data: image, error: dbError } = await supabase
      .from('gallery_images')
      .insert({
        ...body,
        gallery_id: id,
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving gallery image to database:', dbError);
      return NextResponse.json(
        { error: 'Failed to save gallery image' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: image });
  } catch (error) {
    console.error('Error in POST /api/admin/galleries/[id]/images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
