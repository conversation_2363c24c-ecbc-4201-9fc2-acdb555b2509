import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess } from '@/lib/auth-server';
import { performanceMonitor } from '@/lib/performance-monitor';
import { globalCacheManager } from '@/lib/cache-manager';
import { logAdminAccess, getRequestContext } from '@/lib/audit-logger';
import { handleApiError } from '@/lib/error-handler';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET /api/admin/performance - Get performance metrics and statistics
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin authentication and permissions - only super admins can access performance data
    const { user, hasAccess } = await verifyAdminAccess('performance', 'read'); // Use specific permission instead of proxy

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required to view performance data.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with validation
    const timeRangeParam = searchParams.get('timeRange') || '3600000';
    const timeRange = Math.max(60000, Math.min(86400000, parseInt(timeRangeParam, 10) || 3600000)); // 1 min to 24 hours
    const includeCache = searchParams.get('includeCache') === 'true';
    const includeDetails = searchParams.get('includeDetails') === 'true';

    // Get performance statistics
    const performanceStats = performanceMonitor.getStatistics(timeRange);
    
    // Get cache statistics if requested
    let cacheStats = null;
    if (includeCache) {
      cacheStats = globalCacheManager.getAllStats();
    }

    // Get system information
    const systemInfo = {
      timestamp: new Date().toISOString(),
      timeRange: timeRange,
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    };

    // Calculate performance summary
    const summary = {
      totalMetrics: performanceStats.total,
      averageResponseTime: performanceStats.averages.api_response_time || 0,
      averageDbQueryTime: performanceStats.averages.database_query || 0,
      averagePageLoadTime: performanceStats.averages.page_load || 0,
      totalThresholdViolations: Object.values(performanceStats.thresholdViolations).reduce((sum, count) => sum + count, 0),
      healthScore: calculateHealthScore(performanceStats),
    };

    // Prepare response data
    const responseData: any = {
      success: true,
      summary,
      systemInfo,
      performance: {
        statistics: performanceStats,
        thresholds: {
          api_response_time: { good: 200, warning: 500, critical: 1000 },
          database_query: { good: 100, warning: 300, critical: 1000 },
          page_load: { good: 1000, warning: 3000, critical: 5000 },
        },
      },
    };

    if (includeCache) {
      responseData.cache = cacheStats;
    }

    if (includeDetails) {
      responseData.details = {
        slowestOperations: performanceStats.slowestOperations,
        memoryBreakdown: {
          rss: `${(systemInfo.memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
          heapTotal: `${(systemInfo.memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
          heapUsed: `${(systemInfo.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
          external: `${(systemInfo.memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,
        },
      };
    }

    // Performance dashboard access is a read operation - no audit logging needed

    return NextResponse.json(responseData);

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// POST /api/admin/performance - Clear performance metrics or cache
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin authentication and permissions
    const { user, hasAccess } = await verifyAdminAccess('performance', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;
    const requestContext = getRequestContext(request);

    const result: any = { success: true };

    switch (action) {
      case 'clear_metrics': {
        performanceMonitor.clearMetrics();
        result.message = 'Performance metrics cleared';
        break;
      }

      case 'clear_cache': {
        globalCacheManager.clearAll();
        result.message = 'All caches cleared';
        break;
      }

      case 'cleanup_cache': {
        const cleanupResults = globalCacheManager.cleanupAll();
        const totalCleaned = Object.values(cleanupResults).reduce((sum: number, count: number) => sum + count, 0);
        result.message = `Cleaned up ${totalCleaned} expired cache entries`;
        result.details = cleanupResults;
        break;
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: clear_metrics, clear_cache, cleanup_cache' },
          { status: 400 }
        );
    }

    // Log the action
    await logAdminAccess(
      user.id,
      user.username || 'Unknown',
      'performance_dashboard',
      `performance action: ${action}`,
      {
        ...requestContext,
        metadata: {
          action,
          result,
        },
      }
    );

    return NextResponse.json(result);

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

/**
 * Calculate overall system health score based on performance metrics
 */
function calculateHealthScore(stats: any): number {
  let score = 100;
  
  // Deduct points for threshold violations
  const totalViolations = Object.values(stats.thresholdViolations).reduce((sum: number, count: unknown) => sum + (count as number), 0);
  score -= Math.min(totalViolations * 5, 50); // Max 50 points deduction for violations
  
  // Deduct points for slow average response times
  if (stats.averages.api_response_time > 1000) {
    score -= 20;
  } else if (stats.averages.api_response_time > 500) {
    score -= 10;
  }
  
  // Deduct points for slow database queries
  if (stats.averages.database_query > 1000) {
    score -= 15;
  } else if (stats.averages.database_query > 300) {
    score -= 7;
  }
  
  // Deduct points for slow page loads
  if (stats.averages.page_load > 5000) {
    score -= 15;
  } else if (stats.averages.page_load > 3000) {
    score -= 7;
  }
  
  return Math.max(score, 0);
}
