import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { sendInquiryNotification, InquiryEmailData } from '@/lib/email';
import type { CreateInquiryData } from '@/types/database';
import { InquiryStatus } from '@/types/inquiry';
import { apiRateLimit, withRateLimit } from '@/lib/rate-limit';
import { sanitizeInput } from '@/lib/security';
import {
  handleApiError,
  ValidationError,
  formatErrorResponse,
  logError,
  validateRequired,
  validateEmail,
  getClientIP
} from '@/lib/error-handler';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/inquiries - Get inquiries (Admin only)
async function handleGetInquiries(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse and validate query parameters
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10', 10)));
    const status = sanitizeInput(searchParams.get('status') || '');
    const inquiryType = sanitizeInput(searchParams.get('inquiryType') || '');

    // Build query
    let query = supabase
      .from('inquiries')
      .select(`
        id,
        name,
        email,
        phone,
        subject,
        message,
        inquiry_type,
        status,
        created_at
      `, { count: 'exact' });

    // Apply filters
    if (status && status !== 'all') {
      // Validate that status is one of the allowed values
      const validStatus = ['new', 'in_progress', 'resolved', 'closed'].includes(status) 
        ? status as InquiryStatus 
        : 'new';
      query = query.eq('status', validStatus);
    }
    if (inquiryType && inquiryType !== 'all') {
      query = query.eq('inquiry_type', inquiryType);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: inquiries, error, count } = await query;

    if (error) {
      console.error('Error fetching inquiries:', error);
      throw new Error('Failed to fetch inquiries from database');
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: inquiries || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    const appError = handleApiError(error);
    logError(appError, {
      endpoint: '/api/inquiries',
      method: 'GET',
      ip: getClientIP(request)
    });

    return NextResponse.json(
      formatErrorResponse(appError),
      { status: appError.statusCode }
    );
  }
}

// POST /api/inquiries - Create a new inquiry
async function handleCreateInquiry(request: NextRequest) {
  try {
    const body: CreateInquiryData = await request.json();

    // Validate required fields
    validateRequired(body.name, 'Name');
    validateRequired(body.email, 'Email');
    validateRequired(body.message, 'Message');
    validateEmail(body.email);

    // Sanitize inputs
    const sanitizedData = {
      name: sanitizeInput(body.name?.trim() || ''),
      email: sanitizeInput(body.email?.trim() || ''),
      phone: body.phone ? sanitizeInput(body.phone.trim()) : null,
      subject: body.subject ? sanitizeInput(body.subject.trim()) : null,
      message: sanitizeInput(body.message?.trim() || ''),
      inquiry_type: body.inquiry_type ? sanitizeInput(body.inquiry_type.trim()) : 'General Inquiry',
      metadata: body.metadata || {}
    };

    // Additional validation
    if (sanitizedData.name.length < 2) {
      throw new ValidationError('Name must be at least 2 characters long');
    }
    if (sanitizedData.message.length < 10) {
      throw new ValidationError('Message must be at least 10 characters long');
    }

    const supabase = createServerSupabase();

    // Save inquiry to database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .insert({
        name: sanitizedData.name,
        email: sanitizedData.email,
        phone: sanitizedData.phone,
        subject: sanitizedData.subject,
        message: sanitizedData.message,
        inquiry_type: sanitizedData.inquiry_type,
        status: 'new',
        metadata: {
          ...sanitizedData.metadata,
          source: 'api_inquiries',
          ip: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
          timestamp: new Date().toISOString()
        }
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving inquiry to database:', dbError);
      throw new Error('Failed to save inquiry to database');
    }

    // Log the saved inquiry to terminal
    console.log('💾 INQUIRY SAVED TO DATABASE:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('Database Row:', JSON.stringify(inquiry, null, 2));
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Send email notification
    try {
      // Convert null values to undefined to match the InquiryEmailData type
      const emailData: InquiryEmailData = {
        id: inquiry.id,
        name: inquiry.name,
        email: inquiry.email,
        phone: inquiry.phone || undefined,
        subject: inquiry.subject || undefined,
        message: inquiry.message,
        inquiry_type: inquiry.inquiry_type || 'General Inquiry',
        created_at: inquiry.created_at || new Date().toISOString()
      };
      await sendInquiryNotification(emailData);
    } catch (emailError) {
      console.error('❌ Error sending email notification:', emailError);
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      success: true,
      message: 'Inquiry submitted successfully. We will get back to you soon!',
      data: inquiry
    }, { status: 201 });
  } catch (error) {
    const appError = handleApiError(error);
    logError(appError, {
      endpoint: '/api/inquiries',
      method: 'POST',
      ip: getClientIP(request)
    });

    return NextResponse.json(
      formatErrorResponse(appError),
      { status: appError.statusCode }
    );
  }
}

// Apply rate limiting to endpoints
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests, please try again later.'
};

export const GET = withRateLimit(apiRateLimit, handleGetInquiries, rateLimitConfig);
export const POST = withRateLimit(apiRateLimit, handleCreateInquiry, rateLimitConfig);


