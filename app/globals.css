@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    margin: 0;
    padding: 0;
  }

  body {
    @apply font-sans text-gray-900 antialiased;
    margin: 0;
    padding: 0;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 focus:ring-primary-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-16 lg:py-24;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  .hero-text {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight;
  }
  
  .section-title {
    @apply text-3xl sm:text-4xl font-bold text-center mb-12;
  }
  
  .section-subtitle {
    @apply text-lg text-gray-600 text-center max-w-3xl mx-auto mb-16;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
  
  .animation-delay-800 {
    animation-delay: 800ms;
  }
  
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }

  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20;
  }

  .text-gradient-coral {
    @apply bg-gradient-to-r from-coral-500 to-orange-500 bg-clip-text text-transparent;
  }

  .text-gradient-teal {
    @apply bg-gradient-to-r from-primary-500 to-teal-500 bg-clip-text text-transparent;
  }

  .text-gradient-rainbow {
    @apply bg-gradient-to-r from-coral-500 via-orange-500 to-teal-500 bg-clip-text text-transparent;
  }

  .btn-gradient {
    @apply bg-gradient-to-r from-coral-500 to-orange-500 hover:from-coral-600 hover:to-orange-600 text-white font-bold rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-xl;
  }

  .btn-gradient-teal {
    @apply bg-gradient-to-r from-primary-600 to-teal-600 hover:from-primary-700 hover:to-teal-700 text-white font-bold rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-xl;
  }

  .card-modern {
    @apply bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-gray-100;
  }

  .backdrop-glass {
    @apply bg-black/20 backdrop-blur-xl border border-white/20 shadow-2xl;
  }

  /* Mobile optimizations */
  .mobile-safe-area {
    @apply pb-safe-area-inset-bottom;
  }

  .mobile-touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .mobile-scroll-container {
    @apply overflow-x-auto scrollbar-hide;
    scroll-snap-type: x mandatory;
  }

  .mobile-scroll-item {
    scroll-snap-align: start;
  }

  /* Prevent zoom on input focus on iOS */
  @media screen and (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="search"],
    textarea,
    select {
      font-size: 16px !important;
    }
  }

  /* Better mobile card spacing */
  @media screen and (max-width: 640px) {
    .card-modern {
      @apply mx-2 rounded-2xl;
    }

    .container-custom {
      @apply px-3;
    }
  }

  /* Prose content overflow fixes */
  .prose {
    @apply overflow-hidden;
  }

  .prose * {
    @apply max-w-full;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  .prose pre {
    @apply overflow-x-auto;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .prose code {
    @apply break-all;
  }

  .prose table {
    @apply w-full overflow-x-auto block;
    white-space: nowrap;
  }

  .prose img {
    @apply max-w-full h-auto;
  }

  /* Enhanced accessibility styles */
  .keyboard-nav *:focus {
    @apply outline-2 outline-blue-500 outline-offset-2;
  }

  .high-contrast {
    filter: contrast(150%);
  }

  .reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* Focus indicators for better accessibility */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    @apply outline-2 outline-blue-500 outline-offset-2;
  }

  /* Screen reader only class */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .sr-only.focus:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .card {
      @apply border-2 border-gray-900;
    }

    .btn {
      @apply border-2;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Minimal Splash Screen Animations */
@keyframes gentle-fade {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes subtle-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
  }
  66% {
    transform: translateY(5px) rotate(240deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Smooth transitions for route changes */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}
