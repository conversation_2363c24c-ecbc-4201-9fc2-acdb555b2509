{"name": "positive7-tourism-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "postinstall": "node ./scripts/postinstall.js", "analyze": "cross-env ANALYZE=true next build", "build:analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.4", "@tanstack/react-query": "^5.80.6", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/dompurify": "^3.0.5", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.0.1", "bcryptjs": "^2.4.3", "cloudinary": "^2.6.1", "clsx": "^2.0.0", "cross-env": "^7.0.3", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "express-rate-limit": "^7.5.0", "framer-motion": "^11.0.0", "fs-extra": "^11.3.0", "googleapis": "^140.0.1", "lucide-react": "^0.294.0", "next": "^15.3.3", "next-cloudinary": "^6.16.0", "nodemailer": "^6.9.9", "postcss": "^8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "recharts": "^2.15.3", "sharp": "^0.34.2", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.0", "typescript": "^5", "uuid": "^11.1.0", "web-vitals": "^5.0.1", "zod": "^3.25.46"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tanstack/react-query-devtools": "^5.79.2", "@types/bcryptjs": "^2.4.6", "@types/eslint": "^8", "@types/fs-extra": "^11.0.4", "eslint": "^8", "eslint-config-next": "^15.3.3"}, "engines": {"node": ">=22.0.0"}, "resolutions": {"react": "^18.2.0", "react-dom": "^18.2.0"}}