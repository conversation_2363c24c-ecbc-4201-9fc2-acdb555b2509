import { useQueryClient, useMutation } from '@tanstack/react-query';
import { Trip, TripDifficulty } from '@/types/trip';
import { ClientTrip, toClientTrip } from '@/types/client-trip';
import { handleClientError } from '@/lib/error-handler';
import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for trip management
 */

// Query keys for consistent caching
export const TRIP_QUERY_KEYS = {
  trips: ['trips'] as const,
  trip: (id: string) => ['trips', id] as const,
  tripsPaginated: (params: TripQueryParams) => ['trips', 'paginated', params] as const,
  publicTrips: ['public-trips'] as const,
} as const;

// Types for query parameters
export interface TripQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  destination?: string;
  difficulty?: TripDifficulty | 'all';
  isActive?: string | null;
  isFeatured?: boolean;
}

export interface PaginatedTripsResponse {
  data: Trip[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CreateTripRequest {
  title: string;
  description: string | null;
  destination: string;
  duration_days: number;
  difficulty: TripDifficulty;
  price_per_person: number;
  max_participants: number;
  featured_image_url?: string | null;
  itinerary?: any[] | null;
  inclusions?: string[] | null;
  exclusions?: string[] | null;
  is_active?: boolean | null;
  is_featured?: boolean | null;
}

export interface UpdateTripRequest extends Partial<CreateTripRequest> {
  id: string;
}

// API functions
const fetchTrips = async (params: TripQueryParams = {}): Promise<PaginatedTripsResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.set('page', params.page.toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.search) searchParams.set('search', params.search);
  if (params.destination && params.destination !== 'all') searchParams.set('destination', params.destination);
  if (params.difficulty && params.difficulty !== 'all') searchParams.set('difficulty', params.difficulty);
  if (params.isActive !== null && params.isActive !== undefined) {
    searchParams.set('isActive', params.isActive);
  }
  if (params.isFeatured !== undefined) searchParams.set('isFeatured', params.isFeatured.toString());

  const response = await fetch(`/api/admin/trips?${searchParams}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch trips`);
  }

  return response.json();
};

const fetchTrip = async (id: string): Promise<Trip> => {
  const response = await fetch(`/api/admin/trips/${id}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch trip`);
  }

  const data = await response.json();
  return data.data;
};

const createTrip = async (tripData: CreateTripRequest): Promise<Trip> => {
  const response = await fetch('/api/admin/trips', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(tripData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to create trip`);
  }

  const data = await response.json();
  return data.data;
};

const updateTrip = async ({ id, ...tripData }: UpdateTripRequest): Promise<Trip> => {
  const response = await fetch(`/api/admin/trips/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(tripData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update trip`);
  }

  const data = await response.json();
  return data.data;
};

const deleteTrip = async (id: string): Promise<void> => {
  const response = await fetch(`/api/admin/trips/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete trip`);
  }
};

// React Query hooks
export const useTrips = (params: TripQueryParams = {}) => {
  return useStandardQuery<PaginatedTripsResponse>(
    [...TRIP_QUERY_KEYS.tripsPaginated(params)],
    () => fetchTrips(params),
    {
      errorMessage: 'Failed to fetch trips',
      placeholderData: (previousData) => previousData,
    }
  );
};

export const useTrip = (id: string, enabled: boolean = true) => {
  return useStandardQuery<Trip>(
    [...TRIP_QUERY_KEYS.trip(id)],
    () => fetchTrip(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch trip details',
    }
  );
};

export const useCreateTrip = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, CreateTripRequest>(
    createTrip,
    {
      successMessage: 'Trip created successfully',
      errorMessage: 'Failed to create trip',
      invalidateQueries: [
        ['trips'],
        [...TRIP_QUERY_KEYS.publicTrips],
        ['trips', 'paginated'],
      ],
      onSuccess: (newTrip) => {
        // Add the new trip to the cache immediately
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(newTrip.id), newTrip);

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-trips'] });
        queryClient.invalidateQueries({ queryKey: ['public-trip'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

export const useUpdateTrip = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, UpdateTripRequest>(
    updateTrip,
    {
      successMessage: 'Trip updated successfully',
      errorMessage: 'Failed to update trip',
      invalidateQueries: [
        ['trips'],
        [...TRIP_QUERY_KEYS.publicTrips],
        ['trips', 'paginated'],
      ],
      optimisticUpdate: (tripData) => {
        // Optimistically update the trip in cache
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(tripData.id), (oldData: Trip | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, ...tripData };
        });
      },
      onSuccess: (updatedTrip) => {
        // Update with real data from server
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(updatedTrip.id), updatedTrip);

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-trips'] });
        queryClient.invalidateQueries({ queryKey: ['public-trip'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

export const useDeleteTrip = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, string>(
    deleteTrip,
    {
      successMessage: 'Trip deleted successfully',
      errorMessage: 'Failed to delete trip',
      invalidateQueries: [
        ['trips'],
        [...TRIP_QUERY_KEYS.publicTrips],
        ['trips', 'paginated'],
      ],
      optimisticUpdate: (deletedId) => {
        // Optimistically remove from cache
        queryClient.removeQueries({ queryKey: TRIP_QUERY_KEYS.trip(deletedId) });
      },
      onSuccess: (_, deletedId) => {
        // Ensure removal from cache
        queryClient.removeQueries({ queryKey: TRIP_QUERY_KEYS.trip(deletedId) });

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-trips'] });
        queryClient.invalidateQueries({ queryKey: ['public-trip'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

// Hook for toggling trip active status
export const useToggleTripStatus = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, { id: string; isActive: boolean }>(
    async ({ id, isActive }) => {
      // First get the current trip data
      const currentTrip = queryClient.getQueryData(TRIP_QUERY_KEYS.trip(id)) as Trip;

      if (!currentTrip) {
        // If not in cache, fetch it
        const trip = await fetchTrip(id);
        return updateTrip({ ...trip, id, is_active: isActive });
      }

      return updateTrip({ ...currentTrip, id, is_active: isActive });
    },
    {
      successMessage: 'Trip status updated successfully',
      errorMessage: 'Failed to update trip status',
      invalidateQueries: [
        ['trips'],
        [...TRIP_QUERY_KEYS.publicTrips],
        ['trips', 'paginated'],
      ],
      optimisticUpdate: ({ id, isActive }) => {
        // Optimistically update the trip status
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), (oldData: Trip | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, is_active: isActive };
        });
      },
      onSuccess: (updatedTrip, { id }) => {
        // Update with real data from server
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), updatedTrip);

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-trips'] });
        queryClient.invalidateQueries({ queryKey: ['public-trip'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

// Hook for toggling trip featured status
export const useToggleTripFeatured = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, { id: string; isFeatured: boolean }>(
    async ({ id, isFeatured }) => {
      // First get the current trip data
      const currentTrip = queryClient.getQueryData(TRIP_QUERY_KEYS.trip(id)) as Trip;

      if (!currentTrip) {
        // If not in cache, fetch it
        const trip = await fetchTrip(id);
        return updateTrip({ ...trip, id, is_featured: isFeatured });
      }

      return updateTrip({ ...currentTrip, id, is_featured: isFeatured });
    },
    {
      successMessage: 'Trip featured status updated successfully',
      errorMessage: 'Failed to update trip featured status',
      invalidateQueries: [
        ['trips'],
        [...TRIP_QUERY_KEYS.publicTrips],
        ['trips', 'paginated'],
      ],
      optimisticUpdate: ({ id, isFeatured }) => {
        // Optimistically update the trip featured status
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), (oldData: Trip | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, is_featured: isFeatured };
        });
      },
      onSuccess: (updatedTrip, { id }) => {
        // Update with real data from server
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), updatedTrip);

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-trips'] });
        queryClient.invalidateQueries({ queryKey: ['public-trip'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

// Hook for public trips (no authentication required)
export const usePublicTrips = (params: Omit<TripQueryParams, 'isActive'> = {}) => {
  return useStandardQuery(
    ['public-trips', params],
    async () => {
      const searchParams = new URLSearchParams();

      if (params.page) searchParams.set('page', params.page.toString());
      if (params.limit) searchParams.set('limit', params.limit.toString());
      if (params.search) searchParams.set('search', params.search);
      if (params.destination && params.destination !== 'all') searchParams.set('destination', params.destination);
      if (params.difficulty && params.difficulty !== 'all') searchParams.set('difficulty', params.difficulty);
      if (params.isFeatured !== undefined) searchParams.set('isFeatured', params.isFeatured.toString());

      const response = await fetch(`/api/trips?${searchParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch public trips');
      }

      return response.json();
    },
    {
      staleTime: 3 * 1000, // 3 seconds for real-time updates
      gcTime: 30 * 1000, // 30 seconds
      errorMessage: 'Failed to fetch public trips',
      refetchOnWindowFocus: true, // This causes refetch when switching tabs
      refetchOnMount: true, // Always refetch on component mount
      refetchInterval: 8 * 1000, // Auto-refetch every 8 seconds for real-time updates
    }
  );
};

// Hook for public trip detail (no authentication required)
export const usePublicTrip = (slug: string, enabled: boolean = true) => {
  return useStandardQuery<ClientTrip>(
    ['public-trip', slug],
    async () => {
      const response = await fetch(`/api/trips/${slug}`);

      if (!response.ok) {
        throw new Error('Failed to fetch trip');
      }

      const data = await response.json();
      // Transform to client trip to exclude internal fields
      return toClientTrip(data);
    },
    {
      enabled: enabled && !!slug,
      errorMessage: 'Failed to fetch trip details',
      staleTime: 3 * 1000, // 3 seconds for real-time updates
      gcTime: 30 * 1000, // 30 seconds
      refetchOnWindowFocus: true, // This causes refetch when switching tabs
      refetchOnMount: true,
      refetchInterval: 8 * 1000, // Auto-refetch every 8 seconds
    }
  );
};

// Note: Error handling is now built into useStandardMutation
// No need for additional error handling wrappers
