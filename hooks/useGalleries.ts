import { useQueryClient } from '@tanstack/react-query';
import { Gallery, GalleryWithImages } from '@/types/gallery';
import { handleClientError } from '@/lib/error-handler';
import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for gallery management
 */

// Query keys for consistent caching
export const GALLERY_QUERY_KEYS = {
  galleries: ['galleries'] as const,
  gallery: (id: string) => ['galleries', id] as const,
  galleriesPaginated: (params: GalleryQueryParams) => ['galleries', 'paginated', params] as const,
  galleryImages: (id: string) => ['galleries', id, 'images'] as const,
} as const;

// Types for query parameters
export interface GalleryQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  is_active?: string;
  trip_id?: string;
}

export interface GalleryWithImageCount extends Gallery {
  image_count: number;
  trips?: {
    id: string;
    title: string;
    destination: string;
  };
}

export interface PaginatedGalleriesResponse {
  data: GalleryWithImageCount[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CreateGalleryRequest {
  name: string;
  description?: string;
  trip_id?: string;
  is_active?: boolean;
  featured_image_url?: string;
}

export interface UpdateGalleryRequest extends Partial<CreateGalleryRequest> {
  id: string;
}

export interface GalleryImage {
  id: string;
  gallery_id: string;
  image_url: string;
  cloudinary_public_id: string;
  order_index: number; // Database field is 'order_index'
  created_at: string;
  updated_at: string;
}

// API functions
const fetchGalleries = async (params: GalleryQueryParams = {}): Promise<PaginatedGalleriesResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.set('page', params.page.toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.search) searchParams.set('search', params.search);
  if (params.is_active && params.is_active !== 'all') searchParams.set('is_active', params.is_active);
  if (params.trip_id) searchParams.set('trip_id', params.trip_id);

  const response = await fetch(`/api/admin/galleries?${searchParams}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch galleries`);
  }

  return response.json();
};

const fetchGallery = async (id: string): Promise<GalleryWithImageCount> => {
  const response = await fetch(`/api/admin/galleries/${id}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch gallery`);
  }

  const data = await response.json();
  return data.data;
};

const fetchGalleryImages = async (id: string): Promise<GalleryImage[]> => {
  const response = await fetch(`/api/admin/galleries/${id}/images`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch gallery images`);
  }

  const data = await response.json();
  return data.data;
};

const createGallery = async (galleryData: CreateGalleryRequest): Promise<Gallery> => {
  const response = await fetch('/api/admin/galleries', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(galleryData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to create gallery`);
  }

  const data = await response.json();
  return data.data;
};

const updateGallery = async ({ id, ...galleryData }: UpdateGalleryRequest): Promise<Gallery> => {
  const response = await fetch(`/api/admin/galleries/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(galleryData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update gallery`);
  }

  const data = await response.json();
  return data.data;
};

const deleteGallery = async (id: string): Promise<void> => {
  const response = await fetch(`/api/admin/galleries/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete gallery`);
  }
};

const uploadGalleryImage = async (galleryId: string, formData: FormData): Promise<GalleryImage> => {
  const response = await fetch(`/api/admin/galleries/${galleryId}/images`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to upload image`);
  }

  const data = await response.json();
  return data.data;
};

const deleteGalleryImage = async (galleryId: string, imageId: string): Promise<void> => {
  const response = await fetch(`/api/admin/galleries/${galleryId}/images/${imageId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete image`);
  }
};

// Strongly typed batch upload response
interface BatchUploadResponse {
  data: GalleryImage[];
  message: string;
}

const batchUploadGalleryImages = async (
  galleryId: string,
  images: Array<{ imageUrl: string; cloudinaryPublicId: string }>
): Promise<GalleryImage[]> => {
  const response = await fetch(`/api/admin/galleries/${galleryId}/images/batch`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      images: images.map(img => ({
        image_url: img.imageUrl,
        cloudinary_public_id: img.cloudinaryPublicId,
      }))
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to save images`);
  }

  const data: BatchUploadResponse = await response.json();
  return data.data;
};

// React Query hooks using modern patterns
export const useGalleries = (params: GalleryQueryParams = {}) => {
  return useStandardQuery<PaginatedGalleriesResponse>(
    [...GALLERY_QUERY_KEYS.galleriesPaginated(params)],
    () => fetchGalleries(params),
    {
      errorMessage: 'Failed to fetch galleries',
      placeholderData: (previousData) => previousData,
    }
  );
};

export const useGallery = (id: string, enabled: boolean = true) => {
  return useStandardQuery<GalleryWithImageCount>(
    [...GALLERY_QUERY_KEYS.gallery(id)],
    () => fetchGallery(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch gallery details',
      staleTime: 3 * 1000, // 3 seconds for real-time updates
      gcTime: 30 * 1000, // 30 seconds
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    }
  );
};

export const useGalleryImages = (id: string, enabled: boolean = true) => {
  return useStandardQuery<GalleryImage[]>(
    [...GALLERY_QUERY_KEYS.galleryImages(id)],
    () => fetchGalleryImages(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch gallery images',
      staleTime: 3 * 1000, // 3 seconds for real-time updates
      gcTime: 30 * 1000, // 30 seconds
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    }
  );
};

export const useCreateGallery = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Gallery, Error, CreateGalleryRequest>(
    createGallery,
    {
      successMessage: 'Gallery created successfully',
      errorMessage: 'Failed to create gallery',
      invalidateQueries: [
        [...GALLERY_QUERY_KEYS.galleries],
        ['galleries', 'paginated'],
      ],
      onSuccess: (newGallery: Gallery) => {
        // Add the new gallery to the cache immediately
        queryClient.setQueryData(GALLERY_QUERY_KEYS.gallery(newGallery.id), newGallery);

        // Invalidate all public gallery caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
      },
    }
  );
};

export const useUpdateGallery = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Gallery, Error, UpdateGalleryRequest>(
    updateGallery,
    {
      successMessage: 'Gallery updated successfully',
      errorMessage: 'Failed to update gallery',
      invalidateQueries: [
        [...GALLERY_QUERY_KEYS.galleries],
        ['galleries', 'paginated'],
      ],
      optimisticUpdate: (galleryData) => {
        // Optimistically update the gallery in cache
        queryClient.setQueryData(GALLERY_QUERY_KEYS.gallery(galleryData.id), (oldData: Gallery | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, ...galleryData };
        });
      },
      onSuccess: (updatedGallery: Gallery) => {
        // Update with real data from server
        queryClient.setQueryData(GALLERY_QUERY_KEYS.gallery(updatedGallery.id), updatedGallery);

        // Invalidate all public gallery caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
      },
    }
  );
};

export const useDeleteGallery = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, string>(
    deleteGallery,
    {
      successMessage: 'Gallery deleted successfully',
      errorMessage: 'Failed to delete gallery',
      invalidateQueries: [
        [...GALLERY_QUERY_KEYS.galleries],
        ['galleries', 'paginated'],
      ],
      optimisticUpdate: (deletedId) => {
        // Optimistically remove from cache
        queryClient.removeQueries({ queryKey: GALLERY_QUERY_KEYS.gallery(deletedId) });
        queryClient.removeQueries({ queryKey: GALLERY_QUERY_KEYS.galleryImages(deletedId) });
      },
      onSuccess: (_, deletedId) => {
        // Ensure removal from cache
        queryClient.removeQueries({ queryKey: GALLERY_QUERY_KEYS.gallery(deletedId) });
        queryClient.removeQueries({ queryKey: GALLERY_QUERY_KEYS.galleryImages(deletedId) });

        // Invalidate all public gallery caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
      },
    }
  );
};

export const useUploadGalleryImage = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<any, Error, { galleryId: string; formData: FormData }>(
    ({ galleryId, formData }) => uploadGalleryImage(galleryId, formData),
    {
      successMessage: 'Image uploaded successfully',
      errorMessage: 'Failed to upload image',
      invalidateQueries: [
        [...GALLERY_QUERY_KEYS.galleries],
        ['galleries', 'paginated'],
        ['gallery-images'],
      ],
      onSuccess: (newImage) => {
        // Invalidate specific gallery images and gallery data
        queryClient.invalidateQueries({ queryKey: GALLERY_QUERY_KEYS.galleryImages(newImage.gallery_id) });
        queryClient.invalidateQueries({ queryKey: GALLERY_QUERY_KEYS.gallery(newImage.gallery_id) });

        // Invalidate all public gallery caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
      },
    }
  );
};

export const useDeleteGalleryImage = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, { galleryId: string; imageId: string }>(
    ({ galleryId, imageId }) => deleteGalleryImage(galleryId, imageId),
    {
      successMessage: 'Image deleted successfully',
      errorMessage: 'Failed to delete image',
      invalidateQueries: [
        [...GALLERY_QUERY_KEYS.galleries],
        ['galleries', 'paginated'],
        ['gallery-images'],
      ],
      optimisticUpdate: ({ galleryId, imageId }) => {
        // Optimistically remove the image from cache
        queryClient.invalidateQueries({ queryKey: GALLERY_QUERY_KEYS.galleryImages(galleryId) });
      },
      onSuccess: (_, { galleryId }) => {
        // Invalidate specific gallery data
        queryClient.invalidateQueries({ queryKey: GALLERY_QUERY_KEYS.galleryImages(galleryId) });
        queryClient.invalidateQueries({ queryKey: GALLERY_QUERY_KEYS.gallery(galleryId) });

        // Invalidate all public gallery caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
      },
    }
  );
};

export const useBatchUploadGalleryImages = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<GalleryImage[], Error, { galleryId: string; images: Array<{ imageUrl: string; cloudinaryPublicId: string }> }>(
    ({ galleryId, images }) => batchUploadGalleryImages(galleryId, images),
    {
      successMessage: 'Successfully added images to gallery',
      errorMessage: 'Failed to save images to gallery',
      invalidateQueries: [
        [...GALLERY_QUERY_KEYS.galleries],
        ['galleries', 'paginated'],
        ['gallery-images'],
      ],
      onSuccess: (uploadedImages, { galleryId }) => {
        // Invalidate specific gallery images and gallery data
        queryClient.invalidateQueries({ queryKey: GALLERY_QUERY_KEYS.galleryImages(galleryId) });
        queryClient.invalidateQueries({ queryKey: GALLERY_QUERY_KEYS.gallery(galleryId) });

        // Invalidate all public gallery caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
      },
    }
  );
};

// Hook for public galleries (no authentication required)
export const usePublicGalleries = (params: Omit<GalleryQueryParams, 'is_active'> = {}) => {
  return useStandardQuery<{ data: any[], galleries: GalleryWithImages[] }>(
    ['public-galleries', params],
    async () => {
      const searchParams = new URLSearchParams();

      if (params.page) searchParams.set('page', params.page.toString());
      if (params.limit) searchParams.set('limit', params.limit.toString());
      if (params.search) searchParams.set('search', params.search);

      const response = await fetch(`/api/galleries?${searchParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch public galleries');
      }

      return response.json();
    },
    {
      staleTime: 3 * 1000, // 3 seconds for real-time updates
      gcTime: 30 * 1000, // 30 seconds
      errorMessage: 'Failed to fetch public galleries',
      refetchOnWindowFocus: true, // This causes refetch when switching tabs
      refetchOnMount: true, // Always refetch on component mount
      refetchInterval: 8 * 1000, // Auto-refetch every 8 seconds for real-time updates
    }
  );
};

// Hook for public gallery detail (no authentication required)
export const usePublicGallery = (id: string, enabled: boolean = true) => {
  return useStandardQuery<GalleryWithImages>(
    ['public-gallery', id],
    async () => {
      const response = await fetch(`/api/galleries?gallery_id=${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch gallery');
      }

      const data = await response.json();
      return data;
    },
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch gallery details',
      staleTime: 3 * 1000, // 3 seconds for real-time updates
      gcTime: 30 * 1000, // 30 seconds
      refetchOnWindowFocus: true, // This causes refetch when switching tabs
      refetchOnMount: true,
      refetchInterval: 8 * 1000, // Auto-refetch every 8 seconds
    }
  );
};

// Note: Error handling is now built into useStandardMutation
// No need for additional error handling wrappers
