import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for admin user management
 */

// Query keys for consistent caching
export const ADMIN_USER_QUERY_KEYS = {
  users: ['admin-users'] as const,
  roles: ['admin-roles'] as const,
  user: (id: string) => ['admin-users', id] as const,
} as const;

// Types
export interface AdminUser {
  id: string;
  username: string | null;
  full_name: string | null;
  is_active: boolean;
  last_login_at: string | null;
  created_at: string;
  roles: Array<{
    id: string;
    name: string;
    description: string | null;
    permissions: Record<string, string[]>;
  }>;
}

export interface Role {
  id: string;
  name: string;
  description: string | null;
  permissions: Record<string, string[]>;
}

export interface CreateUserData {
  email: string;
  password: string;
  full_name?: string;
  username?: string;
  role_names: string[];
}

export interface UpdateUserData {
  role_names?: string[];
  is_active?: boolean;
  full_name?: string;
  username?: string;
}

// API functions
const fetchAdminUsers = async (): Promise<{ users: AdminUser[] }> => {
  const response = await fetch('/api/admin/users');
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch admin users`);
  }
  
  return response.json();
};

const fetchAdminRoles = async (): Promise<{ roles: Role[] }> => {
  const response = await fetch('/api/admin/roles');
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch admin roles`);
  }
  
  return response.json();
};

const createAdminUser = async (userData: CreateUserData): Promise<AdminUser> => {
  const response = await fetch('/api/admin/users', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to create admin user`);
  }

  const data = await response.json();
  return data.user; // Return just the user object, not the full response
};

const updateAdminUser = async ({ userId, updates }: { userId: string; updates: UpdateUserData }): Promise<AdminUser> => {
  const response = await fetch(`/api/admin/users/${userId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updates),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update admin user`);
  }

  // The API only returns { success: true }, so we need to return a placeholder
  // The actual data will be refreshed via query invalidation
  return { id: userId, ...updates } as AdminUser;
};

const deleteAdminUser = async (userId: string): Promise<void> => {
  const response = await fetch(`/api/admin/users/${userId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete admin user`);
  }
};

// React Query hooks
export const useAdminUsers = () => {
  return useStandardQuery<{ users: AdminUser[] }>(
    [...ADMIN_USER_QUERY_KEYS.users],
    fetchAdminUsers,
    {
      errorMessage: 'Failed to fetch admin users',
      staleTime: 2 * 60 * 1000, // 2 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

export const useAdminRoles = () => {
  return useStandardQuery<{ roles: Role[] }>(
    [...ADMIN_USER_QUERY_KEYS.roles],
    fetchAdminRoles,
    {
      errorMessage: 'Failed to fetch admin roles',
      staleTime: 5 * 60 * 1000, // 5 minutes (roles change less frequently)
      gcTime: 15 * 60 * 1000, // 15 minutes
    }
  );
};

export const useCreateAdminUser = () => {
  return useStandardMutation<AdminUser, Error, CreateUserData>(
    createAdminUser,
    {
      successMessage: 'Admin user created successfully',
      errorMessage: 'Failed to create admin user',
      invalidateQueries: [
        [...ADMIN_USER_QUERY_KEYS.users]
      ],
    }
  );
};

export const useUpdateAdminUser = () => {
  return useStandardMutation<AdminUser, Error, { userId: string; updates: UpdateUserData }>(
    updateAdminUser,
    {
      successMessage: 'Admin user updated successfully',
      errorMessage: 'Failed to update admin user',
      invalidateQueries: [
        [...ADMIN_USER_QUERY_KEYS.users]
      ],
    }
  );
};

export const useDeleteAdminUser = () => {
  return useStandardMutation<void, Error, string>(
    deleteAdminUser,
    {
      successMessage: 'Admin user deleted successfully',
      errorMessage: 'Failed to delete admin user',
      invalidateQueries: [
        [...ADMIN_USER_QUERY_KEYS.users]
      ],
    }
  );
};
