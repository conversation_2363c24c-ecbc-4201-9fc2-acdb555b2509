import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

interface PerformanceStats {
  total: number;
  averages: {
    api_response_time: number;
    database_query: number;
    page_load: number;
  };
  thresholdViolations: Record<string, number>;
  slowestOperations: Array<{
    operation: string;
    duration: number;
    timestamp: string;
  }>;
}

interface SystemInfo {
  timestamp: string;
  timeRange: number;
  nodeVersion: string;
  platform: string;
  uptime: number;
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
}

interface PerformanceSummary {
  totalMetrics: number;
  averageResponseTime: number;
  averageDbQueryTime: number;
  averagePageLoadTime: number;
  totalThresholdViolations: number;
  healthScore: number;
}

interface PerformanceData {
  success: boolean;
  summary: PerformanceSummary;
  systemInfo: SystemInfo;
  performance: {
    statistics: PerformanceStats;
    thresholds: {
      api_response_time: { good: number; warning: number; critical: number };
      database_query: { good: number; warning: number; critical: number };
      page_load: { good: number; warning: number; critical: number };
    };
  };
  cache?: any;
  details?: {
    slowestOperations: Array<any>;
    memoryBreakdown: {
      rss: string;
      heapTotal: string;
      heapUsed: string;
      external: string;
    };
  };
}

interface PerformanceQueryParams {
  timeRange?: number;
  includeCache?: boolean;
  includeDetails?: boolean;
}

// Hook for fetching performance data
export function usePerformance(params: PerformanceQueryParams = {}) {
  const { timeRange = 3600000, includeCache = false, includeDetails = false } = params;
  
  const queryParams = new URLSearchParams({
    timeRange: timeRange.toString(),
    includeCache: includeCache.toString(),
    includeDetails: includeDetails.toString(),
  });

  return useStandardQuery<PerformanceData>(
    ['performance', timeRange.toString(), includeCache.toString(), includeDetails.toString()],
    async () => {
      const response = await fetch(`/api/admin/performance?${queryParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch performance data');
      }

      return response.json();
    },
    {
      staleTime: 30000, // 30 seconds
      refetchInterval: 60000, // Refetch every minute
    }
  );
}

// Hook for performance actions (clear metrics, clear cache, etc.)
export function usePerformanceActions() {
  return useStandardMutation<any, Error, { action: string }>(
    async ({ action }) => {
      const response = await fetch('/api/admin/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ action }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to perform action');
      }

      return response.json();
    },
    {
      successMessage: 'Action completed successfully',
      errorMessage: 'Action failed',
    }
  );
}
