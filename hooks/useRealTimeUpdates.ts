import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook to provide real-time updates without requiring tab switching
 * This addresses the issue where changes only appear when switching tabs
 */

interface UseRealTimeUpdatesOptions {
  /**
   * Interval in milliseconds for checking updates
   * Default: 5000 (5 seconds)
   */
  interval?: number;
  
  /**
   * Whether to enable real-time updates
   * Default: true
   */
  enabled?: boolean;
  
  /**
   * Query keys to invalidate on updates
   * Default: all public query keys
   */
  queryKeys?: string[][];
  
  /**
   * Whether to use visibility API to pause when tab is not visible
   * Default: true
   */
  pauseWhenHidden?: boolean;
}

export function useRealTimeUpdates(options: UseRealTimeUpdatesOptions = {}) {
  const {
    interval = 5000, // 5 seconds
    enabled = true,
    queryKeys = [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ],
    pauseWhenHidden = true
  } = options;

  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isVisibleRef = useRef(true);

  // Track page visibility
  useEffect(() => {
    if (!pauseWhenHidden) return;

    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;
      
      // If page becomes visible, immediately invalidate queries
      if (!document.hidden && enabled) {
        queryKeys.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [queryClient, queryKeys, enabled, pauseWhenHidden]);

  // Set up real-time update interval
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    const performUpdate = () => {
      // Only update if page is visible (unless pauseWhenHidden is false)
      if (pauseWhenHidden && !isVisibleRef.current) {
        return;
      }

      // Invalidate all specified query keys
      queryKeys.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });
    };

    // Set up interval
    intervalRef.current = setInterval(performUpdate, interval);

    // Cleanup on unmount or dependency change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [queryClient, queryKeys, interval, enabled, pauseWhenHidden]);

  // Manual trigger function
  const triggerUpdate = () => {
    queryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
    });
  };

  return {
    triggerUpdate,
    isEnabled: enabled,
    interval
  };
}

/**
 * Hook specifically for public pages that need real-time updates
 * This provides a more aggressive update strategy for public-facing content
 */
export function usePublicRealTimeUpdates(enabled: boolean = true, customInterval?: number) {
  return useRealTimeUpdates({
    interval: customInterval || 30000, // 30 seconds - more reasonable for production
    enabled,
    queryKeys: [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ],
    pauseWhenHidden: true // Pause when tab is not visible to save resources
  });
}

/**
 * Hook for admin pages to trigger immediate updates
 * This can be used in admin components to force immediate cache invalidation
 */
export function useAdminRealTimeUpdates() {
  const queryClient = useQueryClient();

  const forceUpdate = () => {
    // Nuclear option - clear all caches
    queryClient.clear();
    
    // Or more targeted approach
    const publicQueryKeys = [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ];

    publicQueryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
      queryClient.refetchQueries({ queryKey });
    });
  };

  const softUpdate = () => {
    // Gentler approach - just invalidate
    const publicQueryKeys = [
      ['public-galleries'],
      ['public-gallery'],
      ['public-trips'],
      ['public-trip'],
      ['public-photo-albums'],
      ['public-blogs'],
      ['public-blog']
    ];

    publicQueryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
    });
  };

  return {
    forceUpdate,
    softUpdate
  };
}
