import { useQueryClient } from '@tanstack/react-query';
import { PhotoAlbum, CreatePhotoAlbumRequest, UpdatePhotoAlbumRequest } from '@/types/photo-album';
import { handleClientError } from '@/lib/error-handler';
import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for photo album management
 */

// Query keys for consistent caching
export const QUERY_KEYS = {
  photoAlbums: ['photo-albums'] as const,
  photoAlbum: (id: string) => ['photo-albums', id] as const,
  photoAlbumsPaginated: (params: AlbumQueryParams) => ['photo-albums', 'paginated', JSON.stringify(params)] as const,
} as const;

// Types for query parameters
export interface AlbumQueryParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface PaginatedAlbumsResponse {
  albums: PhotoAlbum[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API functions
const fetchPhotoAlbums = async (params: AlbumQueryParams = {}): Promise<PaginatedAlbumsResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.set('page', params.page.toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.search) searchParams.set('search', params.search);

  const response = await fetch(`/api/admin/photo-albums?${searchParams}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch albums`);
  }

  const data = await response.json();
  return {
    albums: data.albums || [],
    pagination: data.pagination || { page: 1, limit: 10, total: 0, totalPages: 0 },
  };
};

const fetchPhotoAlbum = async (id: string): Promise<PhotoAlbum> => {
  const response = await fetch(`/api/admin/photo-albums/${id}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch album`);
  }

  const data = await response.json();
  return data.album;
};

const createPhotoAlbum = async (albumData: CreatePhotoAlbumRequest): Promise<PhotoAlbum> => {
  const response = await fetch('/api/admin/photo-albums', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(albumData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to create album`);
  }

  const data = await response.json();
  return data.album;
};

const updatePhotoAlbum = async (id: string, albumData: UpdatePhotoAlbumRequest): Promise<PhotoAlbum> => {
  const response = await fetch(`/api/admin/photo-albums/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(albumData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update album`);
  }

  const data = await response.json();
  return data.album;
};

const deletePhotoAlbum = async (id: string): Promise<void> => {
  const response = await fetch(`/api/admin/photo-albums/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete album`);
  }
};

// React Query hooks using modern patterns
export const usePhotoAlbums = (params: AlbumQueryParams = {}) => {
  return useStandardQuery<PaginatedAlbumsResponse>(
    [...QUERY_KEYS.photoAlbumsPaginated(params)],
    () => fetchPhotoAlbums(params),
    {
      errorMessage: 'Failed to fetch photo albums',
      placeholderData: (previousData) => previousData,
    }
  );
};

export const usePhotoAlbum = (id: string, enabled: boolean = true) => {
  return useStandardQuery<PhotoAlbum>(
    [...QUERY_KEYS.photoAlbum(id)],
    () => fetchPhotoAlbum(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch photo album',
    }
  );
};

export const useCreatePhotoAlbum = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<PhotoAlbum, Error, CreatePhotoAlbumRequest>(
    createPhotoAlbum,
    {
      successMessage: 'Photo album created successfully',
      errorMessage: 'Failed to create photo album',
      invalidateQueries: [
        [...QUERY_KEYS.photoAlbums],
        ['photo-albums', 'paginated'],
      ],
      onSuccess: (newAlbum) => {
        // Add the new album to the cache immediately
        queryClient.setQueryData([...QUERY_KEYS.photoAlbum(newAlbum.id)], newAlbum);

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

export const useUpdatePhotoAlbum = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<PhotoAlbum, Error, { id: string; data: UpdatePhotoAlbumRequest }>(
    ({ id, data }) => updatePhotoAlbum(id, data),
    {
      successMessage: 'Photo album updated successfully',
      errorMessage: 'Failed to update photo album',
      invalidateQueries: [
        [...QUERY_KEYS.photoAlbums],
        ['photo-albums', 'paginated'],
      ],
      optimisticUpdate: ({ id, data }) => {
        // Optimistically update the album in cache
        queryClient.setQueryData([...QUERY_KEYS.photoAlbum(id)], (oldData: PhotoAlbum | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, ...data };
        });
      },
      onSuccess: (updatedAlbum) => {
        // Update with real data from server
        queryClient.setQueryData([...QUERY_KEYS.photoAlbum(updatedAlbum.id)], updatedAlbum);

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

export const useDeletePhotoAlbum = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, string>(
    deletePhotoAlbum,
    {
      successMessage: 'Album deleted from database. Note: Due to Google Photos API limitations, you must manually delete the album from Google Photos at photos.google.com',
      errorMessage: 'Failed to delete photo album',
      invalidateQueries: [
        [...QUERY_KEYS.photoAlbums],
        ['photo-albums', 'paginated'],
      ],
      optimisticUpdate: (deletedId) => {
        // Optimistically remove from cache
        queryClient.removeQueries({ queryKey: [...QUERY_KEYS.photoAlbum(deletedId)] });
      },
      onSuccess: (_, deletedId) => {
        // Ensure removal from cache
        queryClient.removeQueries({ queryKey: [...QUERY_KEYS.photoAlbum(deletedId)] });

        // Invalidate all public caches with comprehensive patterns
        queryClient.invalidateQueries({ queryKey: ['public-photo-albums'] });
        queryClient.invalidateQueries({ queryKey: ['public-galleries'] });
        queryClient.invalidateQueries({ queryKey: ['public-gallery'] });
      },
    }
  );
};

// Hook for password verification
export const useVerifyAlbumPassword = () => {
  return useStandardMutation<any, Error, { albumId: string; password: string }>(
    async ({ albumId, password }) => {
      const response = await fetch('/api/trips-photos/verify-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ albumId, password }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Password verification failed');
      }

      return response.json();
    },
    {
      successMessage: 'Password verified successfully',
      errorMessage: 'Password verification failed',
    }
  );
};

// Hook for public albums (no authentication required)
export const usePublicPhotoAlbums = () => {
  return useStandardQuery(
    ['public-photo-albums'],
    async () => {
      const response = await fetch('/api/trips-photos/albums');

      if (!response.ok) {
        throw new Error('Failed to fetch public albums');
      }

      return response.json();
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes - more reasonable for production
      gcTime: 10 * 60 * 1000, // 10 minutes
      errorMessage: 'Failed to fetch public photo albums',
      refetchOnWindowFocus: true, // This causes refetch when switching tabs
      refetchOnMount: true, // Always refetch on component mount
      refetchInterval: false, // Disable automatic refetching - use manual triggers instead
    }
  );
};

// Utility hook for optimistic updates
export const useOptimisticAlbumUpdate = () => {
  const queryClient = useQueryClient();

  const updateAlbumOptimistically = (id: string, updates: Partial<PhotoAlbum>) => {
    queryClient.setQueryData(
      QUERY_KEYS.photoAlbum(id),
      (oldData: PhotoAlbum | undefined) => {
        if (!oldData) return oldData;
        return { ...oldData, ...updates };
      }
    );
  };

  const revertOptimisticUpdate = (id: string) => {
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.photoAlbum(id) });
  };

  return {
    updateAlbumOptimistically,
    revertOptimisticUpdate,
  };
};

// Note: Error handling is now built into useStandardMutation
// No need for additional error handling wrappers
