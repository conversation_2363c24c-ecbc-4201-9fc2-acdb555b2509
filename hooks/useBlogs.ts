import { useQueryClient } from '@tanstack/react-query';
import { BlogPost } from '@/types/blog';
import { handleClientError } from '@/lib/error-handler';
import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for blog management
 */

// Query keys for consistent caching
export const BLOG_QUERY_KEYS = {
  blogs: ['blogs'] as const,
  blog: (id: string) => ['blogs', id] as const,
  blogsPaginated: (params: BlogQueryParams) => ['blogs', 'paginated', JSON.stringify(params)] as const,
} as const;

// Types for query parameters
export interface BlogQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  isPublished?: string | null;
}

export interface PaginatedBlogsResponse {
  data: BlogPost[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface CreateBlogRequest {
  title: string;
  content: string;
  excerpt?: string;
  featured_image?: string;
  category?: string;
  tags?: string[];
  meta_description?: string;
  is_published?: boolean;
}

export interface UpdateBlogRequest extends Partial<CreateBlogRequest> {
  id: string;
}

// API functions
const fetchBlogs = async (params: BlogQueryParams = {}): Promise<PaginatedBlogsResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.set('page', params.page.toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.search) searchParams.set('search', params.search);
  if (params.category && params.category !== 'all') searchParams.set('category', params.category);
  if (params.isPublished !== null && params.isPublished !== undefined) {
    searchParams.set('isPublished', params.isPublished);
  }

  const response = await fetch(`/api/admin/blogs?${searchParams}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch blogs`);
  }

  return response.json();
};

const fetchBlog = async (id: string): Promise<BlogPost> => {
  const response = await fetch(`/api/admin/blogs/${id}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch blog`);
  }

  const data = await response.json();
  return data.data;
};

const createBlog = async (blogData: CreateBlogRequest): Promise<BlogPost> => {
  const response = await fetch('/api/admin/blogs', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(blogData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to create blog`);
  }

  const data = await response.json();
  return data.data;
};

const updateBlog = async ({ id, ...blogData }: UpdateBlogRequest): Promise<BlogPost> => {
  const response = await fetch(`/api/admin/blogs/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(blogData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update blog`);
  }

  const data = await response.json();
  return data.data;
};

const deleteBlog = async (id: string): Promise<void> => {
  const response = await fetch(`/api/admin/blogs/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete blog`);
  }
};

// React Query hooks using modern patterns
export const useBlogs = (params: BlogQueryParams = {}) => {
  return useStandardQuery<PaginatedBlogsResponse>(
    [...BLOG_QUERY_KEYS.blogsPaginated(params)],
    () => fetchBlogs(params),
    {
      errorMessage: 'Failed to fetch blog posts',
      placeholderData: (previousData) => previousData,
    }
  );
};

export const useBlog = (id: string, enabled: boolean = true) => {
  return useStandardQuery<BlogPost>(
    [...BLOG_QUERY_KEYS.blog(id)],
    () => fetchBlog(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch blog post',
    }
  );
};

export const useCreateBlog = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<BlogPost, Error, CreateBlogRequest>(
    createBlog,
    {
      successMessage: 'Blog post created successfully',
      errorMessage: 'Failed to create blog post',
      invalidateQueries: [
        ['blogs'],
        ['public-blogs'],
        ['blogs', 'paginated'],
        ['public-blog'] // Invalidate individual public blog cache
      ],
      onSuccess: (newBlog) => {
        // Add the new blog to the cache immediately
        queryClient.setQueryData([...BLOG_QUERY_KEYS.blog(newBlog.id)], newBlog);
      },
    }
  );
};

export const useUpdateBlog = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<BlogPost, Error, UpdateBlogRequest>(
    updateBlog,
    {
      successMessage: 'Blog post updated successfully',
      errorMessage: 'Failed to update blog post',
      invalidateQueries: [
        ['blogs'],
        ['public-blogs'],
        ['blogs', 'paginated'],
        ['public-blog'] // Invalidate individual public blog cache
      ],
      optimisticUpdate: (blogData) => {
        // Optimistically update the blog in cache
        queryClient.setQueryData([...BLOG_QUERY_KEYS.blog(blogData.id)], (oldData: BlogPost | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, ...blogData };
        });
      },
      onSuccess: (updatedBlog) => {
        // Update with real data from server
        queryClient.setQueryData([...BLOG_QUERY_KEYS.blog(updatedBlog.id)], updatedBlog);
      },
    }
  );
};

export const useDeleteBlog = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, string>(
    deleteBlog,
    {
      successMessage: 'Blog post deleted successfully',
      errorMessage: 'Failed to delete blog post',
      invalidateQueries: [
        ['blogs'],
        ['public-blogs'],
        ['blogs', 'paginated'],
        ['public-blog'] // Invalidate individual public blog cache
      ],
      optimisticUpdate: (deletedId) => {
        // Optimistically remove from cache
        queryClient.removeQueries({ queryKey: [...BLOG_QUERY_KEYS.blog(deletedId)] });
      },
      onSuccess: (_, deletedId) => {
        // Ensure removal from cache
        queryClient.removeQueries({ queryKey: [...BLOG_QUERY_KEYS.blog(deletedId)] });
      },
    }
  );
};

// Hook for toggling blog publish status
export const useToggleBlogStatus = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<BlogPost, Error, { id: string; isPublished: boolean }>(
    async ({ id, isPublished }) => {
      // First get the current blog data
      const currentBlog = queryClient.getQueryData([...BLOG_QUERY_KEYS.blog(id)]) as BlogPost;

      if (!currentBlog) {
        // If not in cache, fetch it
        const blog = await fetchBlog(id);
        return updateBlog({ ...blog, id, is_published: isPublished });
      }

      return updateBlog({ ...currentBlog, id, is_published: isPublished });
    },
    {
      successMessage: 'Blog status updated successfully',
      errorMessage: 'Failed to update blog status',
      invalidateQueries: [
        ['blogs'],
        ['public-blogs'],
        ['blogs', 'paginated'],
        ['public-blog'] // Invalidate individual public blog cache
      ],
      optimisticUpdate: ({ id, isPublished }) => {
        // Optimistically update the blog status
        queryClient.setQueryData([...BLOG_QUERY_KEYS.blog(id)], (oldData: BlogPost | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, is_published: isPublished };
        });
      },
      onSuccess: (updatedBlog) => {
        // Update with real data from server
        queryClient.setQueryData([...BLOG_QUERY_KEYS.blog(updatedBlog.id)], updatedBlog);
      },
    }
  );
};

// Hook for public blogs (no authentication required)
export const usePublicBlogs = (params: Omit<BlogQueryParams, 'isPublished'> = {}) => {
  return useStandardQuery(
    ['public-blogs', params],
    async () => {
      const searchParams = new URLSearchParams();

      if (params.page) searchParams.set('page', params.page.toString());
      if (params.limit) searchParams.set('limit', params.limit.toString());
      if (params.search) searchParams.set('search', params.search);
      if (params.category && params.category !== 'all') searchParams.set('category', params.category);

      const response = await fetch(`/api/blog?${searchParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch public blogs');
      }

      return response.json();
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes - more reasonable for production
      gcTime: 10 * 60 * 1000, // 10 minutes
      errorMessage: 'Failed to fetch public blogs',
      refetchOnWindowFocus: true, // This causes refetch when switching tabs
      refetchOnMount: true, // Always refetch on component mount
      refetchInterval: false, // Disable automatic refetching - use manual triggers instead
    }
  );
};

// Hook for public blog detail (no authentication required)
export const usePublicBlog = (slug: string, enabled: boolean = true) => {
  return useStandardQuery<BlogPost>(
    ['public-blog', slug],
    async () => {
      const response = await fetch(`/api/blog/${slug}`);

      if (!response.ok) {
        throw new Error('Failed to fetch blog post');
      }

      const data = await response.json();
      return data;
    },
    {
      enabled: enabled && !!slug,
      errorMessage: 'Failed to fetch blog post details',
      staleTime: 5 * 60 * 1000, // 5 minutes - more reasonable for production
      gcTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: true, // This causes refetch when switching tabs
      refetchOnMount: true,
      refetchInterval: false, // Disable automatic refetching - use manual triggers instead
    }
  );
};

// Error handling wrapper for mutations
export const withBlogErrorHandling = <T extends (...args: any[]) => any>(
  mutationHook: T,
  context?: string
): T => {
  return ((...args: Parameters<T>) => {
    const mutation = mutationHook(...args);
    
    return {
      ...mutation,
      mutate: (variables: any, options?: any) => {
        mutation.mutate(variables, {
          ...options,
          onError: (error: any) => {
            const userMessage = handleClientError(error, context);
            console.error(`Blog mutation error${context ? ` (${context})` : ''}:`, userMessage);
            options?.onError?.(error);
          },
        });
      },
      mutateAsync: async (variables: any, options?: any) => {
        try {
          return await mutation.mutateAsync(variables, options);
        } catch (error) {
          const userMessage = handleClientError(error, context);
          console.error(`Blog mutation error${context ? ` (${context})` : ''}:`, userMessage);
          throw error;
        }
      },
    };
  }) as T;
};
