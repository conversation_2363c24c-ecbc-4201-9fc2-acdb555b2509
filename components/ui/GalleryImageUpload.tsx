'use client';

import { useState, useRef, useEffect } from 'react';
import { Upload, X, Loader2, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';
import Button from './Button';

interface GalleryImageUploadProps {
  onUpload: (data: {
    imageUrl: string;
  }) => Promise<void>;
  folder?: string;
  className?: string;
  accept?: string;
  maxSize?: number; // in MB
  placeholder?: string;
}

export default function GalleryImageUpload({
  onUpload,
  folder,
  className = '',
  accept = 'image/*',
  maxSize = 10,
  placeholder = 'Click to select image'
}: GalleryImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    // Validate file size
    const maxSizeBytes = maxSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      setError(`File size must be less than ${maxSize}MB`);
      return;
    }

    setError(null);
    setSelectedFile(file);

    // Create preview
    try {
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);
    } catch (err) {
      console.error('Error creating preview URL:', err);
      setError('Failed to create image preview');
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setUploading(true);
    setError(null);

    try {
      // Upload to Cloudinary
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('folder', folder || 'positive7/galleries/general');

      const response = await fetch('/api/cloudinary/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      
      // Call the onUpload callback with the image URL
      await onUpload({
        imageUrl: result.url,
      });

      // Reset form
      setSelectedFile(null);
      setPreview(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (err) {
      console.error('Upload error:', err);
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const handleRemove = () => {
    if (preview) {
      URL.revokeObjectURL(preview);
    }
    setSelectedFile(null);
    setPreview(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Cleanup object URL on unmount
  useEffect(() => {
    return () => {
      if (preview) {
        URL.revokeObjectURL(preview);
      }
    };
  }, [preview]);

  const handleClick = () => {
    if (!uploading) {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* File Selection Area */}
      <div
        onClick={handleClick}
        className={`
          relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
          ${preview
            ? 'border-dashed border-gray-300 hover:border-gray-400 bg-gray-50'
            : 'border-solid border-blue-300 bg-blue-50 hover:border-blue-400 hover:bg-blue-100 shadow-sm hover:shadow-md'
          }
          ${error ? 'border-red-300 bg-red-50' : ''}
          ${uploading ? 'pointer-events-none opacity-50' : ''}
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          className="hidden"
          disabled={uploading}
        />

        {preview ? (
          <div className="relative">
            <div className="relative w-full h-48 rounded-lg overflow-hidden">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={preview}
                alt="Preview"
                className="w-full h-full object-cover"
              />
            </div>
            
            {!uploading && (
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemove();
                }}
                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-700">
              <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6 mx-auto max-w-xs">
                <Upload className="w-12 h-12 mx-auto mb-3 text-blue-600" />
                <p className="text-sm font-semibold text-blue-900 mb-1">{placeholder}</p>
                <p className="text-xs text-blue-700">
                  PNG, JPG, WebP up to {maxSize}MB
                </p>
                <div className="mt-3">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Click to browse
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Upload Actions - Only show when image is selected */}
      {selectedFile && (
        <div className="flex justify-end space-x-2 p-4 bg-gray-50 rounded-lg">
          <Button
            type="button"
            variant="outline"
            onClick={handleRemove}
            disabled={uploading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleUpload}
            disabled={uploading || !selectedFile}
          >
            {uploading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              'Upload Image'
            )}
          </Button>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="text-red-600 text-sm flex items-center">
          <X className="w-4 h-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
}
