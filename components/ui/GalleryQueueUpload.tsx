'use client';

import { useState, useRef, useCallback } from 'react';
import { Upload, X, Loader2, Image as ImageIcon, CheckCircle, AlertCircle, Trash2 } from 'lucide-react';
import Image from 'next/image';
import Button from './Button';
import { QueueUploadItem } from '@/types/gallery';
import { useToast } from '@/hooks/useToast';

interface GalleryQueueUploadProps {
  onUploadComplete: (uploadedImages: Array<{ imageUrl: string; cloudinaryPublicId: string }>) => Promise<void>;
  folder?: string;
  className?: string;
  accept?: string;
  maxSize?: number; // in MB
  maxFiles?: number; // Default increased to support admin batch uploads
}

export default function GalleryQueueUpload({
  onUploadComplete,
  folder,
  className = '',
  accept = 'image/*',
  maxSize = 10,
  maxFiles = 300 // Increased to support admin batch uploads (200-250+ images)
}: GalleryQueueUploadProps) {
  const [uploadQueue, setUploadQueue] = useState<QueueUploadItem[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    if (files.length === 0) return;

    // Check file count limit
    if (uploadQueue.length + files.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const newItems: QueueUploadItem[] = [];

    files.forEach((file) => {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} is not a valid image file`);
        return;
      }

      // Validate file size
      if (file.size > maxSize * 1024 * 1024) {
        toast.error(`${file.name} is too large. Maximum size is ${maxSize}MB`);
        return;
      }

      // Create preview URL
      const preview = URL.createObjectURL(file);

      const item: QueueUploadItem = {
        id: `${Date.now()}-${Math.random()}`,
        file,
        preview,
        status: 'pending',
        progress: 0
      };

      newItems.push(item);
    });

    setUploadQueue(prev => [...prev, ...newItems]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [uploadQueue.length, maxFiles, maxSize, toast]);

  const removeItem = useCallback((id: string) => {
    setUploadQueue(prev => {
      const item = prev.find(item => item.id === id);
      if (item?.preview) {
        URL.revokeObjectURL(item.preview);
      }
      return prev.filter(item => item.id !== id);
    });
  }, []);

  const clearQueue = useCallback(() => {
    uploadQueue.forEach(item => {
      if (item.preview) {
        URL.revokeObjectURL(item.preview);
      }
    });
    setUploadQueue([]);
  }, [uploadQueue]);

  const retryFailedUploads = useCallback(() => {
    setUploadQueue(prev => prev.map(item =>
      item.status === 'error' ? { ...item, status: 'pending' as const, error: undefined, progress: 0 } : item
    ));
  }, []);

  const clearCompletedUploads = useCallback(() => {
    setUploadQueue(prev => {
      const itemsToRemove = prev.filter(item => item.status === 'completed');
      itemsToRemove.forEach(item => {
        if (item.preview) {
          URL.revokeObjectURL(item.preview);
        }
      });
      return prev.filter(item => item.status !== 'completed');
    });
  }, []);

  const uploadSingleItem = async (item: QueueUploadItem): Promise<{ imageUrl: string; cloudinaryPublicId: string } | null> => {
    try {
      // Update status to uploading with progress simulation
      setUploadQueue(prev => prev.map(i =>
        i.id === item.id ? { ...i, status: 'uploading' as const, progress: 10 } : i
      ));

      // Upload to Cloudinary
      const formData = new FormData();
      formData.append('file', item.file);
      formData.append('folder', folder || 'positive7/galleries/general');

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadQueue(prev => prev.map(i =>
          i.id === item.id && i.status === 'uploading' && i.progress < 80
            ? { ...i, progress: Math.min(i.progress + 10, 80) }
            : i
        ));
      }, 200);

      const response = await fetch('/api/cloudinary/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();

      // Update progress to 90% while processing
      setUploadQueue(prev => prev.map(i =>
        i.id === item.id ? { ...i, progress: 90 } : i
      ));

      // Extract public_id from Cloudinary URL
      const urlParts = result.url.split('/');
      const publicIdWithExtension = urlParts[urlParts.length - 1];
      const publicId = publicIdWithExtension.split('.')[0];
      const folderPath = urlParts.slice(7, -1).join('/');
      const fullPublicId = folderPath ? `${folderPath}/${publicId}` : publicId;

      // Update status to completed
      setUploadQueue(prev => prev.map(i =>
        i.id === item.id ? {
          ...i,
          status: 'completed' as const,
          progress: 100,
          imageUrl: result.url,
          cloudinaryPublicId: fullPublicId
        } : i
      ));

      return {
        imageUrl: result.url,
        cloudinaryPublicId: fullPublicId
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';

      // Update status to error
      setUploadQueue(prev => prev.map(i =>
        i.id === item.id ? {
          ...i,
          status: 'error' as const,
          progress: 0,
          error: errorMessage
        } : i
      ));

      return null;
    }
  };

  const handleUploadAll = async () => {
    const pendingItems = uploadQueue.filter(item => item.status === 'pending');
    
    if (pendingItems.length === 0) {
      toast.error('No files to upload');
      return;
    }

    setIsUploading(true);
    const toastId = toast.loading(`Uploading ${pendingItems.length} images...`);

    try {
      const uploadPromises = pendingItems.map(item => uploadSingleItem(item));
      const results = await Promise.all(uploadPromises);
      
      // Filter out failed uploads
      const successfulUploads = results.filter((result): result is { imageUrl: string; cloudinaryPublicId: string } => 
        result !== null
      );

      if (successfulUploads.length > 0) {
        await onUploadComplete(successfulUploads);
        toast.dismiss(toastId);
        toast.success(`Successfully uploaded ${successfulUploads.length} images`);

        // Show completion status briefly, then clear completed items
        setTimeout(() => {
          setUploadQueue(prev => prev.filter(item => item.status !== 'completed'));
        }, 3000);
      } else {
        toast.dismiss(toastId);
        toast.error('All uploads failed');
      }

      // Show failed uploads count if any
      const failedCount = pendingItems.length - successfulUploads.length;
      if (failedCount > 0) {
        toast.error(`${failedCount} uploads failed. Check individual items for details.`);
      }

    } catch (error) {
      toast.dismiss(toastId);
      toast.error('Failed to save images to gallery');
    } finally {
      setIsUploading(false);
    }
  };

  const handleClick = () => {
    if (!isUploading) {
      fileInputRef.current?.click();
    }
  };

  const getStatusIcon = (status: QueueUploadItem['status']) => {
    switch (status) {
      case 'pending':
        return <ImageIcon className="h-4 w-4 text-gray-400" />;
      case 'uploading':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: QueueUploadItem['status']) => {
    switch (status) {
      case 'pending':
        return 'border-gray-200 bg-gray-50';
      case 'uploading':
        return 'border-blue-200 bg-blue-50';
      case 'completed':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* File Selection Area */}
      <div
        onClick={handleClick}
        className={`
          relative border-2 border-dashed rounded-lg p-6 cursor-pointer transition-all duration-200
          ${isUploading 
            ? 'border-gray-300 bg-gray-100 pointer-events-none opacity-50' 
            : 'border-blue-300 bg-blue-50 hover:border-blue-400 hover:bg-blue-100'
          }
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple
          onChange={handleFileSelect}
          className="hidden"
          disabled={isUploading}
        />

        <div className="text-center">
          <Upload className="mx-auto h-12 w-12 text-blue-400 mb-4" />
          <p className="text-lg font-medium text-gray-900 mb-2">
            Select Multiple Images
          </p>
          <p className="text-sm text-gray-600">
            Click to select up to {maxFiles} images (max {maxSize}MB each)
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Supports: JPG, PNG, GIF, WebP
          </p>
        </div>
      </div>

      {/* Upload Queue */}
      {uploadQueue.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Upload Queue ({uploadQueue.length} files)
              </h3>
              {isUploading && (
                <div className="mt-2">
                  <div className="flex items-center text-sm text-gray-600 mb-1">
                    <span>Overall Progress: {Math.round((uploadQueue.filter(item => item.status === 'completed').length / Math.max(uploadQueue.length, 1)) * 100)}%</span>
                  </div>
                  <div className="w-64 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full transition-all duration-500"
                      style={{
                        width: `${Math.round((uploadQueue.filter(item => item.status === 'completed').length / Math.max(uploadQueue.length, 1)) * 100)}%`
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleUploadAll}
                disabled={isUploading || uploadQueue.filter(item => item.status === 'pending').length === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload All ({uploadQueue.filter(item => item.status === 'pending').length})
                  </>
                )}
              </Button>
              {uploadQueue.filter(item => item.status === 'error').length > 0 && (
                <Button
                  onClick={retryFailedUploads}
                  disabled={isUploading}
                  variant="outline"
                  className="text-orange-600 hover:text-orange-700 border-orange-300 hover:border-orange-400"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Retry Failed ({uploadQueue.filter(item => item.status === 'error').length})
                </Button>
              )}
              {uploadQueue.filter(item => item.status === 'completed').length > 0 && (
                <Button
                  onClick={clearCompletedUploads}
                  disabled={isUploading}
                  variant="outline"
                  className="text-green-600 hover:text-green-700 border-green-300 hover:border-green-400"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Clear Completed ({uploadQueue.filter(item => item.status === 'completed').length})
                </Button>
              )}
              <Button
                onClick={clearQueue}
                disabled={isUploading}
                variant="outline"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All
              </Button>
            </div>
          </div>

          {/* Upload Summary */}
          {uploadQueue.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-3 text-sm">
              <div className="flex justify-between items-center">
                <div className="flex space-x-4">
                  <span className="text-gray-600">
                    Pending: <span className="font-medium text-gray-900">{uploadQueue.filter(item => item.status === 'pending').length}</span>
                  </span>
                  <span className="text-blue-600">
                    Uploading: <span className="font-medium">{uploadQueue.filter(item => item.status === 'uploading').length}</span>
                  </span>
                  <span className="text-green-600">
                    Completed: <span className="font-medium">{uploadQueue.filter(item => item.status === 'completed').length}</span>
                  </span>
                  <span className="text-red-600">
                    Failed: <span className="font-medium">{uploadQueue.filter(item => item.status === 'error').length}</span>
                  </span>
                </div>
                <div className="text-gray-500">
                  Total: {uploadQueue.length} files
                </div>
              </div>
            </div>
          )}

          {/* Queue Items */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {uploadQueue.map((item) => (
              <div
                key={item.id}
                className={`relative rounded-lg border-2 p-2 ${getStatusColor(item.status)}`}
              >
                {/* Remove Button */}
                {item.status !== 'uploading' && (
                  <button
                    onClick={() => removeItem(item.id)}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 z-10"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}

                {/* Image Preview */}
                <div className="aspect-square relative mb-2">
                  <Image
                    src={item.preview}
                    alt={item.file.name}
                    fill
                    className="object-cover rounded"
                  />
                </div>

                {/* Status and Info */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    {getStatusIcon(item.status)}
                    <span className="text-xs text-gray-600 capitalize">
                      {item.status}
                    </span>
                  </div>
                  
                  <p className="text-xs text-gray-700 truncate" title={item.file.name}>
                    {item.file.name}
                  </p>
                  
                  <p className="text-xs text-gray-500">
                    {(item.file.size / 1024 / 1024).toFixed(1)}MB
                  </p>

                  {/* Progress Bar */}
                  {item.status === 'uploading' && (
                    <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                      <div
                        className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${item.progress}%` }}
                      />
                    </div>
                  )}

                  {item.error && (
                    <p className="text-xs text-red-600 truncate" title={item.error}>
                      {item.error}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
