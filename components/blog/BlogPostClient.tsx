'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import DOMPurify from 'dompurify'
import {
  Calendar,
  Clock,
  User,
  Tag,
  ArrowLeft,
  Share2,
  Facebook,
  Twitter,
  Linkedin,
  Copy,
  Check,
  RefreshCw
} from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import { getImageWithFallback } from '@/lib/image-fallbacks'
import { usePublicBlog } from '@/hooks/useBlogs'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

import { BlogPost } from '@/types/database'

interface RelatedPost {
  id: string
  title: string
  slug: string
  excerpt: string | null
  featured_image_url: string | null
  created_at: string | null
  category: string | null
}

interface BlogPostClientProps {
  blogSlug: string
  initialPost?: BlogPost
  relatedPosts: RelatedPost[]
}

// Helper function to calculate reading time more accurately
const calculateReadingTime = (content: string): number => {
  // Remove HTML tags and get plain text
  const plainText = content.replace(/<[^>]*>/g, '');
  // Count words (split by whitespace and filter out empty strings)
  const wordCount = plainText.split(/\s+/).filter(word => word.length > 0).length;
  // Average reading speed is 200-250 words per minute, using 200 for conservative estimate
  const readingTime = Math.ceil(wordCount / 200);
  // Minimum 1 minute reading time
  return Math.max(1, readingTime);
};

export default function BlogPostClient({ blogSlug, initialPost, relatedPosts }: BlogPostClientProps) {
  const [copied, setCopied] = React.useState(false)

  // Use React Query to fetch blog post data with real-time updates
  const { data: post, isLoading, error, refetch } = usePublicBlog(blogSlug, true)

  // Enhanced logic to handle real-time deletions/deactivations
  const currentPost = post || initialPost

  // Check if item was deleted/deactivated during real-time updates
  const isItemDeleted = error && error.message.includes('404') && initialPost
  const isItemUnavailable = error && (
    error.message.includes('not found') ||
    error.message.includes('404') ||
    error.message.includes('unpublished')
  )

  // Loading state
  if (isLoading && !currentPost) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  // Handle case where blog post was deleted/unpublished during real-time updates
  if (isItemDeleted) {
    return (
      <div className="text-center py-12">
        <div className="text-orange-400 mb-4">
          <Tag className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Blog Post No Longer Available</h3>
        <p className="text-gray-600 mb-6">
          This blog post has been removed or unpublished. It may have been deleted by an administrator or temporarily taken offline.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/blog">
            <button className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Browse All Posts
            </button>
          </Link>
          <button
            onClick={() => refetch()}
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Check Again
          </button>
        </div>
      </div>
    )
  }

  // Error state
  if (error && !currentPost) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Tag className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load blog post</h3>
        <p className="text-gray-600 mb-4">
          There was an error loading this blog post. Please try again.
        </p>
        <button
          onClick={() => refetch()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    )
  }

  // Not found state
  if (!currentPost) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Tag className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Blog post not found</h3>
        <p className="text-gray-600">
          This blog post could not be found or may have been removed.
        </p>
      </div>
    )
  }

  const shareUrl = typeof window !== 'undefined' ? window.location.href : ''
  const shareTitle = currentPost.title

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareTitle)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50">
      {/* Back Button */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <Link
            href="/blog"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Blog
          </Link>
        </div>
      </div>

      <article className="max-w-4xl mx-auto px-4 py-8">
        {/* Featured Image */}
        {currentPost.featured_image_url && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="relative h-96 card-modern overflow-hidden mb-8"
          >
            <Image
              src={getImageWithFallback(currentPost.featured_image_url)}
              alt={currentPost.title}
              fill
              className="object-cover"
              priority
            />
          </motion.div>
        )}

        {/* Article Header */}
        <motion.header
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          {/* Category */}
          <div className="mb-4">
            <span className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
              <Tag className="w-3 h-3 mr-1" />
              {currentPost.category}
            </span>
          </div>

          {/* Title */}
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            {currentPost.title}
          </h1>

          {/* Excerpt */}
          {currentPost.excerpt && (
            <p className="text-xl text-gray-600 mb-6 leading-relaxed">
              {currentPost.excerpt}
            </p>
          )}

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-gray-500 text-sm">
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              <span>By {currentPost.author}</span>
            </div>
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>{new Date(currentPost.created_at || new Date().toISOString()).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              <span>{calculateReadingTime(currentPost.content)} min read</span>
            </div>
          </div>

          {/* Share Buttons */}
          <div className="flex items-center gap-4 mt-6 pt-6 border-t border-gray-200">
            <span className="text-sm font-medium text-gray-700 flex items-center">
              <Share2 className="w-4 h-4 mr-2" />
              Share:
            </span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => { window.open(shareLinks.facebook, '_blank'); }}
                className="p-2"
              >
                <Facebook className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => { window.open(shareLinks.twitter, '_blank'); }}
                className="p-2"
              >
                <Twitter className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => { window.open(shareLinks.linkedin, '_blank'); }}
                className="p-2"
              >
                <Linkedin className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyLink}
                className="p-2"
              >
                {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </motion.header>

        {/* Article Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="prose prose-lg max-w-full mb-12 overflow-hidden break-words"
        >
          {/* Runtime-level sanitized HTML content with DOMPurify */}
          <div dangerouslySetInnerHTML={{
            __html: (() => {
              // Always sanitize content, both client and server side
              if (typeof window !== 'undefined') {
                // Client-side sanitization with DOMPurify
                return DOMPurify.sanitize(currentPost.content, {
                  ALLOWED_TAGS: [
                    'p', 'br', 'strong', 'em', 'u', 's', 'strike',
                    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                    'ul', 'ol', 'li',
                    'a', 'img',
                    'blockquote', 'code', 'pre',
                    'div', 'span',
                    'sub', 'sup'
                  ],
                  ALLOWED_ATTR: [
                    'href', 'src', 'alt', 'title', 'target', 'rel',
                    'style', 'class',
                    'align'
                  ],
                  ALLOW_DATA_ATTR: false
                });
              } else {
                // Server-side basic sanitization - strip script tags and dangerous attributes
                return currentPost.content
                  .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                  .replace(/on\w+="[^"]*"/gi, '')
                  .replace(/javascript:/gi, '');
              }
            })()
          }} />
        </motion.div>

        {/* Tags */}
        {currentPost.tags && currentPost.tags.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="mb-12 pt-8 border-t border-gray-200"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {currentPost.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 transition-colors"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </motion.div>
        )}

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="pt-12 border-t border-gray-200"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <Link
                  key={relatedPost.id}
                  href={`/blog/${relatedPost.slug}`}
                  className="group"
                >
                  <article className="card-modern overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                    {relatedPost.featured_image_url && (
                      <div className="relative h-48">
                        <Image
                          src={getImageWithFallback(relatedPost.featured_image_url)}
                          alt={relatedPost.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                    )}
                    <div className="p-6">
                      <div className="mb-2">
                        <span className="text-xs text-blue-600 font-medium">
                          {relatedPost.category}
                        </span>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
                        {relatedPost.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {relatedPost.excerpt}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{new Date(relatedPost.created_at || new Date().toISOString()).toLocaleDateString()}</span>
                        <span>{calculateReadingTime(relatedPost.excerpt || '')} min read</span>
                      </div>
                    </div>
                  </article>
                </Link>
              ))}
            </div>
          </motion.section>
        )}
      </article>
    </div>
  )
}
