'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Award, 
  Users, 
  MapPin, 
  Heart, 
  CheckCircle, 
  ArrowRight,
  BookOpen,
  Shield,
  Star,
  Globe
} from 'lucide-react';
import { COMPANY_INFO, EDUCATIONAL_EXCELLENCE, SAINT_AUGUSTINE_QUOTE } from '@/lib/constants';

const features = [
  {
    icon: BookOpen,
    title: 'Educational Excellence',
    description: 'We believe it\'s not just the exposure to new places that changes student\'s lives, but also the kind of experience they have during that exposure.',
  },
  {
    icon: Shield,
    title: 'Safety First',
    description: 'Gujarat Tourism affiliated with comprehensive safety protocols and experienced guides ensuring secure and memorable journeys.',
  },
  {
    icon: Users,
    title: 'Expert Team',
    description: 'Our passionate team of educators and travel experts craft meaningful experiences that combine learning with adventure.',
  },
  {
    icon: Globe,
    title: 'Diverse Destinations',
    description: 'From spiritual Rishikesh to adventurous Manali, we offer carefully curated destinations across India.',
  },
];

const achievements = [
  {
    number: '1000+',
    label: 'Happy Students',
    icon: Users,
  },
  {
    number: '25+',
    label: 'Schools Partnered',
    icon: Award,
  },
  {
    number: '15+',
    label: 'Destinations',
    icon: MapPin,
  },
  {
    number: '10+',
    label: 'Years Experience',
    icon: Star,
  },
];

export default function AboutSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section id="about-section" className="section-padding bg-white">
      <div className="container-custom">
        {/* Main About Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <div className="mb-6">
              <span className="text-primary-600 font-semibold text-lg">About Positive7</span>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mt-2 mb-4">
                Bringing Learning <span className="text-gradient">To Life</span>
              </h2>
              <div className="w-20 h-1 bg-gradient-to-r from-primary-600 to-secondary-600 rounded"></div>
            </div>

            <p className="text-lg text-gray-600 mb-6 leading-relaxed">
              {COMPANY_INFO.description}
            </p>

            <p className="text-gray-600 mb-8 leading-relaxed">
              {EDUCATIONAL_EXCELLENCE.description}
            </p>

            {/* Key Points */}
            <div className="space-y-4 mb-8">
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-accent-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900">Gujarat Tourism Affiliated</h4>
                  <p className="text-gray-600">Officially recognized and trusted by Gujarat Tourism for quality educational tours.</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-accent-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900">Experiential Learning</h4>
                  <p className="text-gray-600">Hands-on experiences that make learning memorable and impactful for students.</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-6 w-6 text-accent-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900">Comprehensive Programs</h4>
                  <p className="text-gray-600">Educational trips, CAS projects, adventure camps, and workshops tailored for schools.</p>
                </div>
              </div>
            </div>

            <Link
              href="/about"
              className="inline-flex items-center px-8 py-4 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-300 transform hover:scale-105 group"
            >
              Learn More About Us
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </motion.div>

          {/* Images */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="relative h-48 rounded-lg overflow-hidden">
                  <Image
                    src="https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg"
                    alt="Students at Rishikesh"
                    fill
                    className="object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="relative h-32 rounded-lg overflow-hidden">
                  <Image
                    src="https://positive7.in/wp-content/uploads/2024/11/TIRTHAN-VALLEY-JIBHI-1024x697.webp"
                    alt="Tirthan Valley Experience"
                    fill
                    className="object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="relative h-32 rounded-lg overflow-hidden">
                  <Image
                    src="https://positive7.in/wp-content/uploads/2025/01/gettyimages-1134041601-612x612-1.jpg"
                    alt="Manali Adventure"
                    fill
                    className="object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="relative h-48 rounded-lg overflow-hidden">
                  <Image
                    src="https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE.webp"
                    alt="Cultural Learning"
                    fill
                    className="object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
            </div>

            {/* Floating Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="absolute -bottom-6 -left-6 bg-white rounded-lg shadow-xl p-6 border border-gray-100"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <Heart className="h-6 w-6 text-primary-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">10+</div>
                  <div className="text-sm text-gray-600">Years of Excellence</div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Saint Augustine Quote */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="max-w-4xl mx-auto bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 md:p-12">
            <div className="relative">
              <div className="text-6xl text-primary-200 font-serif absolute -top-4 -left-4">"</div>
              <blockquote className="text-xl md:text-2xl font-medium text-gray-800 italic mb-4 relative z-10">
                {SAINT_AUGUSTINE_QUOTE.text.replace(/"/g, '')}
              </blockquote>
              <cite className="text-primary-600 font-semibold">
                — {SAINT_AUGUSTINE_QUOTE.author}
              </cite>
              <div className="text-6xl text-primary-200 font-serif absolute -bottom-8 -right-4">"</div>
            </div>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              variants={itemVariants}
              className="text-center group"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-primary-100 to-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <feature.icon className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Achievements */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-white mb-4">
              Our Journey in Numbers
            </h3>
            <p className="text-primary-100 text-lg">
              Trusted by schools and loved by students across India
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.label}
                variants={itemVariants}
                className="text-center"
              >
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <achievement.icon className="h-8 w-8 text-white" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {achievement.number}
                </div>
                <div className="text-primary-100 font-medium">
                  {achievement.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
