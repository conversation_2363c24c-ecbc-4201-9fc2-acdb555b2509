'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Testimonial } from '@/types/database';

interface TestimonialData {
  id: string;
  name: string;
  rating: number;
  title: string | null;
  content: string;
  image_url: string | null;
  is_featured: boolean;
}

interface TestimonialsSectionProps {
  testimonials: TestimonialData[];
}

// Fallback testimonials if none are provided
const fallbackTestimonials: TestimonialData[] = [
  {
    id: 'fallback-1',
    name: 'Student Parent',
    rating: 5,
    title: 'Amazing Experience',
    content: 'Positive7 provided an incredible educational experience for our children. The attention to detail and care for student safety was exceptional.',
    image_url: null,
    is_featured: true,
  },
  {
    id: 'fallback-2',
    name: 'School Coordinator',
    rating: 5,
    title: 'Professional Service',
    content: 'Working with Positive7 has been a pleasure. Their team is professional, organized, and truly cares about creating meaningful educational experiences.',
    image_url: null,
    is_featured: true,
  },
];

export default function TestimonialsSection({ testimonials }: TestimonialsSectionProps) {
  // Use provided testimonials or fallback
  const displayTestimonials = testimonials.length > 0 ? testimonials : fallbackTestimonials;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-scroll every 15 seconds
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % Math.ceil(displayTestimonials.length / 3));
    }, 15000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, displayTestimonials.length]);

  const totalPages = Math.ceil(displayTestimonials.length / 3);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % totalPages);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + totalPages) % totalPages);
  };

  // Handle keyboard navigation - prevent double navigation
  const handleKeyDown = (event: React.KeyboardEvent, action: 'prev' | 'next') => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.stopPropagation(); // Prevent event bubbling

      // Prevent double navigation by checking if already processing
      if (event.currentTarget.getAttribute('data-processing') === 'true') {
        return;
      }

      // Mark as processing
      event.currentTarget.setAttribute('data-processing', 'true');

      if (action === 'prev') {
        prevTestimonial();
      } else {
        nextTestimonial();
      }

      // Pause auto-play when using keyboard
      setIsAutoPlaying(false);
      setTimeout(() => {
        setIsAutoPlaying(true);
        // Clear processing flag
        event.currentTarget.setAttribute('data-processing', 'false');
      }, 5000); // Resume after 5 seconds
    }
  };



  // Get testimonials for current page (3 at a time)
  const getCurrentTestimonials = () => {
    const startIndex = currentIndex * 3;
    return displayTestimonials.slice(startIndex, startIndex + 3);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-5 w-5',
          i < rating
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        )}
      />
    ));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section className="py-16 bg-gradient-to-br from-slate-50 via-white to-coral-50/30">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-coral-500 to-orange-500 rounded-xl mb-4">
            <Quote className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            What Our <span className="bg-gradient-to-r from-coral-600 to-orange-600 bg-clip-text text-transparent">Students Say</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Real experiences from students, parents, and educators who have joined our educational journeys.
          </p>
        </motion.div>

        {/* Testimonial Cards Grid */}
        <div className="relative px-16 md:px-20">
          {/* Navigation Arrows - Left */}
          {totalPages > 1 && (
            <button
              onClick={prevTestimonial}
              onKeyDown={(e) => handleKeyDown(e, 'prev')}
              onMouseEnter={() => setIsAutoPlaying(false)}
              onMouseLeave={() => setIsAutoPlaying(true)}
              onFocus={() => setIsAutoPlaying(false)}
              onBlur={() => setIsAutoPlaying(true)}
              className="absolute -left-2 md:-left-4 top-1/2 -translate-y-1/2 z-10 p-3 md:p-4 bg-white/95 backdrop-blur-sm text-coral-600 rounded-full shadow-lg hover:shadow-xl focus:shadow-xl transition-all duration-300 hover:scale-110 focus:scale-110 hover:bg-white focus:bg-white border border-coral-100 focus:outline-none focus:ring-2 focus:ring-coral-500 focus:ring-offset-2 group"
              aria-label="Previous testimonials"
            >
              <ChevronLeft className="h-5 w-5 md:h-6 md:w-6 transition-transform group-hover:-translate-x-0.5" />
            </button>
          )}

          {/* Navigation Arrows - Right */}
          {totalPages > 1 && (
            <button
              onClick={nextTestimonial}
              onKeyDown={(e) => handleKeyDown(e, 'next')}
              onMouseEnter={() => setIsAutoPlaying(false)}
              onMouseLeave={() => setIsAutoPlaying(true)}
              onFocus={() => setIsAutoPlaying(false)}
              onBlur={() => setIsAutoPlaying(true)}
              className="absolute -right-2 md:-right-4 top-1/2 -translate-y-1/2 z-10 p-3 md:p-4 bg-white/95 backdrop-blur-sm text-coral-600 rounded-full shadow-lg hover:shadow-xl focus:shadow-xl transition-all duration-300 hover:scale-110 focus:scale-110 hover:bg-white focus:bg-white border border-coral-100 focus:outline-none focus:ring-2 focus:ring-coral-500 focus:ring-offset-2 group"
              aria-label="Next testimonials"
            >
              <ChevronRight className="h-5 w-5 md:h-6 md:w-6 transition-transform group-hover:translate-x-0.5" />
            </button>
          )}

          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className="grid md:grid-cols-3 gap-6"
            >
              {getCurrentTestimonials().map((testimonial, index) => (
                <motion.div
                  key={`${currentIndex}-${index}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden"
                >
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-coral-100 to-orange-100 rounded-full -translate-y-10 translate-x-10 opacity-60"></div>

                  <div className="relative z-10">
                    {/* Rating */}
                    <div className="flex items-center space-x-1 mb-4">
                      {renderStars(testimonial.rating)}
                    </div>

                    {/* Quote Icon */}
                    <Quote className="h-8 w-8 text-coral-400 mb-4 opacity-60" />

                    {/* Content */}
                    <blockquote className="text-gray-700 leading-relaxed mb-6 text-sm">
                      "{testimonial.content}"
                    </blockquote>

                    {/* Author Info */}
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-coral-500 to-orange-500 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 text-sm">
                          {testimonial.name}
                        </h4>
                        <p className="text-coral-600 text-xs">
                          {testimonial.title || 'Valued Customer'}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-12"
        >
          <div className="bg-white rounded-xl p-8 max-w-xl mx-auto shadow-lg">
            <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-3">
              Ready to Create Your Own <span className="bg-gradient-to-r from-coral-600 to-orange-600 bg-clip-text text-transparent">Experience?</span>
            </h3>
            <p className="text-gray-600 mb-6">
              Join thousands of students who have discovered the joy of educational travel.
            </p>
            <Link href="/trips" className="btn-gradient px-8 py-3 inline-flex items-center gap-2 rounded-xl font-semibold">
              Start Your Journey
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
