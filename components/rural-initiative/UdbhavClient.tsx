'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  Quote,
  MapPin,
  Users,
  Heart,
  Lightbulb,
  Target,
  BookOpen,
  Star,
  Globe,
  Mail,
  Phone,
  TreePine,
  Home,
  Palette,
  Music,
  Camera,
  UserCheck,
  Leaf,
  Sun,
  Mountain
} from 'lucide-react';
import Button from '@/components/ui/Button';
import { UDBHAV_INFO } from '@/lib/constants';
import { SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';

interface Initiative {
  icon: any;
  title: string;
  description: string;
  color: string;
}

const initiatives: Initiative[] = [
  {
    icon: Home,
    title: 'Village Homestays',
    description: 'Experience authentic rural life by staying with local families and learning their daily routines',
    color: 'from-blue-500 to-green-500'
  },
  {
    icon: Palette,
    title: 'Traditional Arts & Crafts',
    description: 'Learn pottery, weaving, painting, and other traditional crafts from local artisans',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: Music,
    title: 'Folk Music & Dance',
    description: 'Participate in traditional folk performances and learn regional music and dance forms',
    color: 'from-blue-500 to-indigo-500'
  },
  {
    icon: Leaf,
    title: 'Organic Farming',
    description: 'Hands-on experience with sustainable farming practices and organic agriculture',
    color: 'from-emerald-500 to-green-500'
  },
  {
    icon: BookOpen,
    title: 'Cultural Exchange',
    description: 'Bridge the gap between urban and rural communities through meaningful interactions',
    color: 'from-blue-500 to-green-500'
  },
  {
    icon: UserCheck,
    title: 'Community Development',
    description: 'Participate in local development projects and contribute to rural empowerment',
    color: 'from-green-500 to-emerald-500'
  }
];

export default function UdbhavClient() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <>
      {/* Hero Section */}
      <SectionErrorBoundary context="udbhav-hero">
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="relative py-20 overflow-hidden"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-green-600/10" />

          {/* Floating Background Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-32 h-32 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"></div>
            <div className="absolute top-40 right-20 w-24 h-24 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
            <div className="absolute bottom-20 left-20 w-28 h-28 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '4s' }}></div>
          </div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                transition={{ delay: 0.2 }}
              >
                <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                  <TreePine className="w-4 h-4" aria-hidden="true" />
                  Rural Life Initiative
                </div>
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-black text-gray-900 mb-6">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
                    Udbhav
                  </span>
                  <br />
                  Exploring Rural Life
                </h1>
                <p className="text-lg sm:text-xl text-gray-700 mb-8 leading-relaxed">
                  {UDBHAV_INFO.description} Experience the authentic beauty of rural India through immersive cultural exchanges, traditional arts, and sustainable living practices.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    size="lg"
                    aria-label="Explore Udbhav programs"
                  >
                    Explore Programs
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    aria-label="Join the initiative"
                  >
                    Join the Initiative
                  </Button>
                </div>
              </motion.div>

              <motion.div
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                transition={{ delay: 0.4 }}
                className="relative"
              >
                <div className="relative h-80 sm:h-96 card-modern overflow-hidden shadow-2xl">
                  <Image
                    src={UDBHAV_INFO.images[0]}
                    alt="Udbhav Rural Life Initiative"
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
                </div>
                {/* Floating Stats Cards */}
                <div className="absolute -bottom-4 sm:-bottom-6 -left-4 sm:-left-6 bg-white rounded-xl p-3 sm:p-4 shadow-lg">
                  <div className="text-xl sm:text-2xl font-bold text-green-600">100+</div>
                  <div className="text-xs text-gray-600">Villages Connected</div>
                </div>
                <div className="absolute -top-4 sm:-top-6 -right-4 sm:-right-6 bg-white rounded-xl p-3 sm:p-4 shadow-lg">
                  <div className="text-xl sm:text-2xl font-bold text-blue-600">5K+</div>
                  <div className="text-xs text-gray-600">Students Participated</div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.section>
      </SectionErrorBoundary>

      {/* Quote Section */}
      <SectionErrorBoundary context="udbhav-quote">
        <motion.section
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="py-16 bg-gradient-to-r from-blue-600 to-green-600"
        >
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <Quote className="w-10 sm:w-12 h-10 sm:h-12 text-white/50 mx-auto mb-6" aria-hidden="true" />
            <blockquote className="text-xl sm:text-2xl md:text-3xl font-medium text-white mb-6 leading-relaxed">
              "The world is a book, and those who do not travel read only one page. Through Udbhav, we open
              the chapters of rural wisdom, traditional knowledge, and authentic cultural experiences."
            </blockquote>
            <cite className="text-blue-100 text-base sm:text-lg">- Positive7 Udbhav Initiative</cite>
          </div>
        </motion.section>
      </SectionErrorBoundary>

      {/* About Udbhav Initiative */}
      <SectionErrorBoundary context="udbhav-about">
        <motion.section
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="py-20"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-3 gap-12">
              <motion.div variants={itemVariants} className="lg:col-span-2">
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-8">Bridging Rural and Urban India</h2>

                <div className="prose prose-lg max-w-full text-gray-700 space-y-6 overflow-hidden break-words">
                  <p>
                    The Udbhav initiative represents a revolutionary approach to cultural education and rural-urban connectivity.
                    Born from the belief that authentic learning happens through genuine cultural immersion, Udbhav creates
                    meaningful bridges between India's vibrant rural communities and urban students seeking authentic experiences.
                  </p>

                  <p>
                    Through carefully curated village stays, traditional craft workshops, and cultural exchange programs,
                    participants gain deep insights into rural life, sustainable practices, and time-honored traditions.
                    This isn't just tourism—it's a transformative journey that enriches both visitors and host communities.
                  </p>

                  <p>
                    Our village partners benefit from sustainable tourism income, cultural preservation support, and educational
                    exchanges that bring fresh perspectives to rural areas. Students return with enhanced cultural awareness,
                    practical life skills, and a profound appreciation for India's diverse heritage.
                  </p>

                  <p>
                    The initiative focuses on authentic experiences: learning traditional pottery from master craftsmen,
                    participating in organic farming practices, understanding folk music and dance, and engaging in
                    community development projects that create lasting positive impact.
                  </p>
                </div>
              </motion.div>

              <motion.div variants={itemVariants} className="space-y-8">
                {/* Quick Facts */}
                <div className="card-modern p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Initiative Highlights</h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Home className="w-5 h-5 text-green-600" aria-hidden="true" />
                      <div>
                        <div className="font-medium">Villages Connected</div>
                        <div className="text-sm text-gray-600">100+ Rural Communities</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-blue-600" aria-hidden="true" />
                      <div>
                        <div className="font-medium">Participants</div>
                        <div className="text-sm text-gray-600">5,000+ Students</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Palette className="w-5 h-5 text-purple-600" aria-hidden="true" />
                      <div>
                        <div className="font-medium">Traditional Crafts</div>
                        <div className="text-sm text-gray-600">50+ Art Forms</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Leaf className="w-5 h-5 text-teal-600" aria-hidden="true" />
                      <div>
                        <div className="font-medium">Sustainable Projects</div>
                        <div className="text-sm text-gray-600">200+ Initiatives</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Philosophy */}
                <div className="card-modern p-6 bg-gradient-to-br from-blue-50 via-white to-green-50">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Core Values</h3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <Heart className="w-5 h-5 text-red-500 mt-1" aria-hidden="true" />
                      <div>
                        <div className="font-medium text-gray-900">Authentic Connections</div>
                        <div className="text-sm text-gray-600">Building genuine relationships between communities</div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <UserCheck className="w-5 h-5 text-blue-500 mt-1" aria-hidden="true" />
                      <div>
                        <div className="font-medium text-gray-900">Mutual Respect</div>
                        <div className="text-sm text-gray-600">Honoring traditional wisdom and modern perspectives</div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Leaf className="w-5 h-5 text-green-500 mt-1" aria-hidden="true" />
                      <div>
                        <div className="font-medium text-gray-900">Sustainable Impact</div>
                        <div className="text-sm text-gray-600">Creating lasting positive change for all participants</div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.section>
      </SectionErrorBoundary>

      {/* Program Initiatives */}
      <SectionErrorBoundary context="udbhav-initiatives">
        <motion.section
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="py-20 bg-gray-50"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div variants={itemVariants} className="text-center mb-16">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Our Program Initiatives</h2>
              <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
                Discover the diverse ways Udbhav connects urban students with rural wisdom through immersive experiences
              </p>
            </motion.div>

            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {initiatives.map((initiative, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="card-modern p-6 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
                >
                  <div className={`w-12 h-12 bg-gradient-to-r ${initiative.color} rounded-full flex items-center justify-center mb-4`}>
                    <initiative.icon className="w-6 h-6 text-white" aria-hidden="true" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{initiative.title}</h3>
                  <p className="text-gray-600 text-sm">{initiative.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.section>
      </SectionErrorBoundary>

      {/* Vision for the Future */}
      <SectionErrorBoundary context="udbhav-vision">
        <motion.section
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="py-20"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div variants={itemVariants}>
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">Vision for the Future</h2>
                <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                  As Positive7 continues to grow, Udbhav remains committed to his original vision while embracing
                  new technologies and methodologies that can enhance the educational travel experience.
                </p>

                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Target className="w-4 h-4 text-blue-600" aria-hidden="true" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Expanding Horizons</h3>
                      <p className="text-gray-600">Plans to introduce international educational programs and cultural exchange initiatives</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Lightbulb className="w-4 h-4 text-green-600" aria-hidden="true" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Technology Integration</h3>
                      <p className="text-gray-600">Leveraging AI and digital platforms to create more personalized learning experiences</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Heart className="w-4 h-4 text-purple-600" aria-hidden="true" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Community Impact</h3>
                      <p className="text-gray-600">Strengthening partnerships with local communities to create mutual benefits</p>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div variants={itemVariants} className="card-modern p-8 bg-gradient-to-br from-blue-50 via-white to-green-50">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h3>
                <p className="text-gray-700 mb-6">
                  Udbhav is always excited to connect with educators, parents, and students who share his passion
                  for transformative learning experiences.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                      <Mail className="w-5 h-5 text-white" aria-hidden="true" />
                    </div>
                    <div>
                      <div className="font-medium">Direct Contact</div>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                      <Phone className="w-5 h-5 text-white" aria-hidden="true" />
                    </div>
                    <div>
                      <div className="font-medium">Phone</div>
                      <a
                        href="tel:+917878005500"
                        className="text-sm text-gray-600 hover:text-green-600 transition-colors"
                      >
                        +91 78780 05500
                      </a>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                    Schedule a Meeting
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.section>
      </SectionErrorBoundary>

      {/* Call to Action */}
      <SectionErrorBoundary context="udbhav-cta">
        <motion.section
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="py-20"
        >
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Ready to Create Unforgettable Learning Experiences?
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 mb-8">
              Connect with Udbhav and the Positive7 team to plan your next educational adventure
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
              >
                Schedule a Meeting
              </Button>
              <Button variant="outline" size="lg">
                Download Brochure
              </Button>
            </div>
          </div>
        </motion.section>
      </SectionErrorBoundary>
    </>
  );
}
