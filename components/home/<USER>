'use client'

import React from 'react'
import { usePublicTrips } from '@/hooks/useTrips'
import HeroSection from '@/components/sections/HeroSection'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { Trip } from '@/types/database'

interface HeroTrip {
  id: string
  title: string
  slug: string
  description: string
  destination: string
  featured_image_url: string | null
}

// Minimal trip interface for homepage use
interface MinimalTrip {
  id: string;
  title: string;
  slug: string;
  description: string;
  destination: string;
  days: number;
  nights: number;
  price_per_person: number;
  difficulty: string;
  featured_image_url: string;
  is_featured: boolean;
  is_active: boolean;
}

interface FeaturedTripsClientProps {
  initialTrips?: MinimalTrip[]
}

export default function FeaturedTripsClient({ initialTrips = [] }: FeaturedTripsClientProps) {
  // Use React Query to fetch featured trips with real-time updates
  const { data: tripsData, isLoading, error } = usePublicTrips({
    limit: 10 // Get more trips to ensure we have featured ones
  })

  // Use fetched data if available, otherwise fall back to initial data
  const allTrips = tripsData?.data || initialTrips

  // Filter for featured trips and create hero trips from the first 3
  const featuredTrips = allTrips.filter((trip: Trip | MinimalTrip) => trip.is_featured)
  const heroTrips: HeroTrip[] = featuredTrips.slice(0, 3).map((trip: Trip | MinimalTrip) => ({
    id: trip.id,
    title: trip.title,
    slug: trip.slug,
    description: trip.description || '',
    destination: trip.destination,
    featured_image_url: trip.featured_image_url || '/images/fallback-trip.jpg'
  }))

  // Check if we're in fallback mode (no trips available)
  const isFallbackMode = heroTrips.length === 0

  // Loading state
  if (isLoading && initialTrips.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  // Always render HeroSection - it handles fallback mode internally
  return <HeroSection heroTrips={heroTrips} />
}
