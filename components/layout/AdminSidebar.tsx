'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import {
  Home,
  Map,
  FileText,
  MessageSquare,
  Settings,
  LogOut,
  Users,
  BarChart2,
  Image as ImageIcon,
  X,
  Shield,
  Activity
} from 'lucide-react';

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  text: string;
  isActive: boolean;
  onClose?: () => void;
}

const SidebarLink = ({ href, icon, text, isActive, onClose }: SidebarLinkProps) => (
  <Link 
    href={href as any}
    onClick={onClose}
    className={`
      flex items-center gap-3 px-4 py-3 rounded-lg transition-colors
      ${isActive 
        ? 'bg-blue-100 text-blue-700' 
        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}
    `}
  >
    <div className={isActive ? 'text-blue-700' : 'text-gray-500'}>
      {icon}
    </div>
    <span className="font-medium">{text}</span>
  </Link>
);

interface AdminSidebarProps {
  onClose?: () => void;
}

export default function AdminSidebar({ onClose }: AdminSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { hasPermission, isSuperAdmin, signOut, loading } = useAuth();

  // Check if user is super admin using the proper function
  const isUserSuperAdmin = isSuperAdmin();
  
  const isActive = (path: string) => {
    if (path === '/admin' && pathname === '/admin') return true;
    if (path !== '/admin') {
      // For exact path matching to avoid conflicts like /admin/trips vs /admin/trips-photos
      if (path === '/admin/trips') {
        return pathname === '/admin/trips' || (pathname?.startsWith('/admin/trips/') && !pathname?.startsWith('/admin/trips-photos'));
      }
      if (path === '/admin/trips-photos') {
        return pathname?.startsWith('/admin/trips-photos');
      }
      // For other paths, use the original logic
      return pathname?.startsWith(path);
    }
    return false;
  };

  const handleLogout = async () => {
    try {
      // Use modern Supabase auth signOut
      await signOut();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // Always redirect to login page, even if signOut() throws
      window.location.href = '/admin/login';
    }
  };

  return (
    <div className="h-full flex flex-col bg-white border-r border-gray-200">
      <div className="flex items-center justify-between p-4 lg:p-6">
        <Link href="/admin" className="flex items-center">
          <Image 
            src="/images/positive7-logo.png" 
            width={32}
            height={32}
            className="h-8 w-auto mr-2" 
            alt="Positive7 Admin Logo" 
          />
          <h1 className="text-xl lg:text-2xl font-bold text-gray-900">Admin</h1>
        </Link>
        {onClose && (
          <button 
            className="lg:hidden p-1 rounded-full hover:bg-gray-100"
            onClick={onClose}
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        )}
      </div>
      
      <div className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
        {/* Dashboard - Always visible to all admin users */}
        <SidebarLink
          href="/admin"
          icon={<Home className="w-5 h-5" />}
          text="Dashboard"
          isActive={isActive('/admin')}
          onClose={onClose}
        />

        {/* Trips - Only for users with trips read permission */}
        {!loading && hasPermission('trips', 'read') && (
          <SidebarLink
            href="/admin/trips"
            icon={<Map className="w-5 h-5" />}
            text="Trips"
            isActive={isActive('/admin/trips')}
            onClose={onClose}
          />
        )}

        {/* Trip Photos - Only for users with trip_photos read permission */}
        {!loading && hasPermission('trip_photos', 'read') && (
          <SidebarLink
            href="/admin/trips-photos"
            icon={<ImageIcon className="w-5 h-5" />}
            text="Trip Photos"
            isActive={isActive('/admin/trips-photos')}
            onClose={onClose}
          />
        )}

        {/* Galleries - Only for users with galleries read permission */}
        {!loading && hasPermission('galleries', 'read') && (
          <SidebarLink
            href="/admin/galleries"
            icon={<ImageIcon className="w-5 h-5" />}
            text="Galleries"
            isActive={isActive('/admin/galleries')}
            onClose={onClose}
          />
        )}

        {/* Blogs - Only for users with blog read permission */}
        {!loading && hasPermission('blog', 'read') && (
          <SidebarLink
            href="/admin/blogs"
            icon={<FileText className="w-5 h-5" />}
            text="Blogs"
            isActive={isActive('/admin/blogs')}
            onClose={onClose}
          />
        )}

        {/* Inquiries - Only for users with inquiries read permission */}
        {!loading && hasPermission('inquiries', 'read') && (
          <SidebarLink
            href="/admin/inquiries"
            icon={<MessageSquare className="w-5 h-5" />}
            text="Inquiries"
            isActive={isActive('/admin/inquiries')}
            onClose={onClose}
          />
        )}

        {/* Team Members - Only for users with team_members read permission */}
        {!loading && hasPermission('team_members', 'read') && (
          <SidebarLink
            href="/admin/team-members"
            icon={<Users className="w-5 h-5" />}
            text="Team Members"
            isActive={isActive('/admin/team-members')}
            onClose={onClose}
          />
        )}

        {/* Admin Users - Only for users with users read permission (super admin) */}
        {!loading && hasPermission('users', 'read') && (
          <SidebarLink
            href="/admin/users"
            icon={<Users className="w-5 h-5" />}
            text="Admin Users"
            isActive={isActive('/admin/users')}
            onClose={onClose}
          />
        )}

        {/* Performance - Only for super admins */}
        {!loading && hasPermission('performance', 'read') && (
          <SidebarLink
            href="/admin/performance"
            icon={<Activity className="w-5 h-5" />}
            text="Performance"
            isActive={isActive('/admin/performance')}
            onClose={onClose}
          />
        )}

        {/* Audit Logs - Only for super admins */}
        {!loading && hasPermission('audit_logs', 'read') && (
          <SidebarLink
            href="/admin/audit-logs"
            icon={<Shield className="w-5 h-5" />}
            text="Audit Logs"
            isActive={isActive('/admin/audit-logs')}
            onClose={onClose}
          />
        )}


      </div>
      
      <div className="p-4 border-t border-gray-200">
        <button 
          onClick={handleLogout}
          className="w-full flex items-center gap-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
        >
          <LogOut className="w-5 h-5" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
} 