'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState, ReactNode } from 'react';

interface QueryProviderProps {
  children: ReactNode;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () => {
      try {
        return new QueryClient({
          defaultOptions: {
            queries: {
              staleTime: 5 * 60 * 1000, // 5 minutes - more reasonable for production
              gcTime: 10 * 60 * 1000, // 10 minutes - balanced cache retention
              retry: (failureCount, error: unknown) => {
                // Don't retry on 4xx errors
                const errorWithStatus = error as { status?: number };
                if (errorWithStatus?.status && errorWithStatus.status >= 400 && errorWithStatus.status < 500) {
                  return false;
                }
                // Retry up to 2 times for other errors (less aggressive)
                return failureCount < 2;
              },
              refetchOnWindowFocus: true, // This is why changes appear when switching tabs
              refetchOnMount: true,
              refetchInterval: false, // Disable automatic refetching - use manual triggers instead
            },
            mutations: {
              retry: false,
            },
          },
        });
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to create QueryClient:', error);
        }
        // Return a basic QueryClient as fallback
        return new QueryClient();
      }
    }
  );

  if (!queryClient) {
    if (process.env.NODE_ENV === 'development') {
      console.error('QueryClient is undefined');
    }
    return <>{children}</>;
  }

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
