'use client'

import React, { useState, useCallback } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  MapPin,
  Calendar,
  Mountain,
  Star,
  Search,
  ArrowRight,
  RefreshCw
} from 'lucide-react'
import AdvancedSearch from '@/components/search/AdvancedSearch'
import ScrollReveal from '@/components/animations/ScrollReveal'
import { TripCardSkeleton } from '@/components/ui/SkeletonLoaders'
import ProgressiveImage from '@/components/ui/ProgressiveImage'
import { usePublicTrips } from '@/hooks/useTrips'
import { useDebounce } from '@/hooks/useDebounce'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { Trip } from '@/types/database'

// Trip interface imported from @/types/database

interface TripsClientProps {
  initialTrips?: Trip[]
}

// Utility function to get difficulty color
const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'easy':
      return 'bg-green-100 text-green-800';
    case 'moderate':
      return 'bg-yellow-100 text-yellow-800';
    case 'challenging':
      return 'bg-orange-100 text-orange-800';
    case 'extreme':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-blue-100 text-blue-800';
  }
};

// Format price to Indian Rupees
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0
  }).format(price);
};

export default function TripsClient({ initialTrips = [] }: TripsClientProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchFilters, setSearchFilters] = useState<any>({})
  const [isSearching, setIsSearching] = useState(false)

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(searchQuery, 300)

  // Use React Query to fetch trips with real-time updates
  const { data: tripsData, isLoading, error, refetch } = usePublicTrips({
    search: debouncedSearch || undefined,
    destination: searchFilters.destination || undefined,
    difficulty: searchFilters.difficulty || undefined,
    limit: 50
  })

  // Use fetched data if available, otherwise fall back to initial data
  const trips = tripsData?.data || initialTrips

  // Advanced search handler
  const handleSearch = useCallback((query: string, filters: any) => {
    setIsSearching(true)
    setSearchQuery(query)
    setSearchFilters(filters)

    // Simulate search delay for better UX
    setTimeout(() => {
      setIsSearching(false)
    }, 300)
  }, [])

  // Filter trips based on search and filters
  const filteredTrips = trips.filter((trip: Trip) => {
    // Text search
    if (searchQuery && searchQuery.length > 0) {
      const searchMatch = (
        trip.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trip.destination.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (trip.description && trip.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      if (!searchMatch) return false;
    }

    // Filter by destination
    if (searchFilters.destination && searchFilters.destination !== '') {
      if (!trip.destination.toLowerCase().includes(searchFilters.destination.toLowerCase())) {
        return false;
      }
    }

    // Filter by difficulty
    if (searchFilters.difficulty && searchFilters.difficulty !== '') {
      if (trip.difficulty !== searchFilters.difficulty) {
        return false;
      }
    }

    // Filter by duration
    if (searchFilters.duration && searchFilters.duration !== '') {
      const [min, max] = searchFilters.duration.split('-').map(Number);
      if (max) {
        if (trip.days < min || trip.days > max) return false;
      } else if (searchFilters.duration === '15+') {
        if (trip.days < 15) return false;
      }
    }

    return true;
  });

  // Sort trips based on filters and featured status
  const sortedTrips = [...filteredTrips].sort((a, b) => {
    // Sort by criteria
    if (searchFilters.sortBy) {
      switch (searchFilters.sortBy) {
        case 'price-low':
          return a.price_per_person - b.price_per_person;
        case 'price-high':
          return b.price_per_person - a.price_per_person;
        case 'duration':
          return a.days - b.days;
        case 'popularity':
          // Featured trips are considered more popular
          if (a.is_featured && !b.is_featured) return -1;
          if (!a.is_featured && b.is_featured) return 1;
          return 0;
        default:
          break;
      }
    }

    // Default: featured trips first
    if (a.is_featured && !b.is_featured) return -1;
    if (!a.is_featured && b.is_featured) return 1;
    return 0;
  });

  // Error state
  if (error && trips.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Search className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load trips</h3>
        <p className="text-gray-600 mb-4">
          There was an error loading the trips. Please try again.
        </p>
        <button
          onClick={() => refetch()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-12">
      {/* Hero Header */}
      <ScrollReveal direction="up" duration={0.8}>
        <div className="text-center mb-12">
          <h1 className="text-5xl md:text-6xl lg:text-7xl font-display font-black text-gray-900 mb-6 leading-tight">
            {searchQuery ? (
              <>
                Search Results for{' '}
                <span className="text-gradient-coral">"{searchQuery}"</span>
              </>
            ) : (
              <>
                Explore Our{' '}
                <span className="text-gradient-rainbow">Educational Tours</span>
              </>
            )}
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light">
            Discover amazing educational tours and adventures across India.
            Find your perfect journey with our advanced search and filtering options.
          </p>
        </div>
      </ScrollReveal>

      {/* Advanced Search */}
      <ScrollReveal direction="up" duration={0.8} delay={0.2}>
        <AdvancedSearch
          onSearch={handleSearch}
          results={sortedTrips.slice(0, 5)} // Show top 5 results in dropdown
          loading={isSearching}
          placeholder="Search destinations, activities, or experiences..."
          showFilters={true}
        />
      </ScrollReveal>

      {/* Results Summary */}
      {(searchQuery || Object.keys(searchFilters).length > 0) && (
        <ScrollReveal direction="up" duration={0.6} delay={0.3}>
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {sortedTrips.length} {sortedTrips.length === 1 ? 'trip' : 'trips'} found
                </h3>
                <p className="text-gray-600">
                  {searchQuery && `Searching for "${searchQuery}"`}
                  {Object.keys(searchFilters).length > 0 && ' with filters applied'}
                </p>
              </div>
              {(searchQuery || Object.keys(searchFilters).length > 0) && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    setSearchFilters({})
                  }}
                  className="px-4 py-2 text-coral-600 hover:text-coral-700 font-medium transition-colors"
                >
                  Clear all
                </button>
              )}
            </div>
          </div>
        </ScrollReveal>
      )}

      {/* Trips Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
        {(isSearching || (isLoading && trips.length === 0)) ? (
          // Show skeleton loaders while searching or initial loading - prevent double render
          [...Array(6)].map((_, index) => (
            <TripCardSkeleton key={`skeleton-${index}`} />
          ))
        ) : sortedTrips.length > 0 ? (
          sortedTrips.map((trip, index) => (
            <TripCard
              key={trip.id}
              trip={trip}
              index={index}
            />
          ))
          ) : (
            <div className="col-span-3 bg-white rounded-3xl shadow-lg border border-gray-100 p-16 text-center">
              <div className="text-gray-400 mb-6">
                <Search className="w-20 h-20 mx-auto" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">No trips found</h3>
              <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                We couldn't find any trips matching your criteria. Try adjusting your search or filters.
              </p>
              <button
                onClick={() => {
                  setSearchQuery('')
                  setSearchFilters({})
                }}
                className="btn-gradient px-8 py-3 rounded-2xl font-bold shadow-xl hover:shadow-2xl"
              >
                Clear Search & Filters
              </button>
            </div>
          )}
        </div>
    </div>
  )
}

function TripCard({ trip, index }: { trip: Trip; index: number }) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: Math.min(index * 0.03, 0.3) }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className="group"
    >
      <Link href={`/trips/${trip.slug}`}>
        <div className="card-modern overflow-hidden hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 hover:rotate-1">
          {/* Image */}
          <div className="relative h-72 overflow-hidden">
            <ProgressiveImage
              src={trip.featured_image_url || '/images/fallback-trip.jpg'}
              alt={trip.title}
              fill
              className="object-cover transition-transform duration-1000 group-hover:scale-125"
              priority={index < 3}
            />

            {/* Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
            <div className="absolute inset-0 bg-gradient-to-r from-coral-500/20 via-transparent to-teal-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            {/* Difficulty Badge */}
            <div className="absolute top-6 left-6">
              <span className={`px-4 py-2 text-sm font-bold rounded-2xl backdrop-blur-md border border-white/20 shadow-lg capitalize ${getDifficultyColor(trip.difficulty)}`}>
                {trip.difficulty}
              </span>
            </div>

            {/* Featured Badge */}
            {trip.is_featured && (
              <div className="absolute top-6 right-6">
                <span className="px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-400 text-yellow-900 text-sm font-bold rounded-2xl flex items-center gap-2 shadow-lg">
                  <Star className="w-4 h-4 fill-current" />
                  Featured
                </span>
              </div>
            )}

            {/* Floating Elements */}
            <div className="absolute top-6 left-6 opacity-0 group-hover:opacity-100 transition-all duration-500 delay-200">
              <div className="w-3 h-3 bg-secondary-400 rounded-full animate-float" />
            </div>

            {/* Quick Info Overlay */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{
                opacity: isHovered ? 1 : 0,
                y: isHovered ? 0 : 30
              }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="absolute bottom-6 left-6 right-6"
            >
              <div className="backdrop-glass rounded-2xl p-4 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-secondary-300" />
                    <span className="font-semibold">{trip.days} Days, {trip.nights} Nights</span>
                  </div>
                  <div className="w-2 h-2 bg-coral-400 rounded-full animate-pulse" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Content */}
          <div className="p-8">
            <div className="mb-4">
              <h3 className="text-2xl font-bold text-gray-900 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-coral-600 group-hover:to-teal-600 group-hover:bg-clip-text transition-all duration-500 leading-tight mb-2">
                {trip.title}
              </h3>
              <div className="w-12 h-1 bg-gradient-to-r from-coral-400 to-teal-400 rounded-full group-hover:w-20 transition-all duration-500" />
            </div>

            <div className="flex items-center space-x-2 mb-4 text-gray-600">
              <MapPin className="h-5 w-5 text-coral-500" />
              <span className="text-base font-medium">{trip.destination}</span>
            </div>

            {trip.description && (
              <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed text-base">
                {trip.description}
              </p>
            )}

            {/* Trip details */}
            <div className="flex items-center justify-between mb-6 text-sm text-gray-500">
              <div className="flex items-center space-x-2 bg-gray-50 rounded-xl px-3 py-2">
                <Calendar className="h-4 w-4 text-coral-500" />
                <span className="font-medium">{trip.days} Days, {trip.nights} Nights</span>
              </div>

              {trip.is_trek && (
                <div className="flex items-center space-x-1 bg-green-50 rounded-xl px-3 py-2 text-green-600">
                  <Mountain className="w-4 h-4" />
                  <span className="font-medium">Trek</span>
                </div>
              )}
            </div>

            {/* Price and CTA */}
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-gray-900">
                  {formatPrice(trip.price_per_person)}
                </div>
                <div className="text-sm text-gray-600">per person</div>
              </div>

              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="flex items-center text-coral-600 font-semibold">
                  <span>Explore</span>
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
