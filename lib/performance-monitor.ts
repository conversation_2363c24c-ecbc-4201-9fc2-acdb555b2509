/**
 * Performance monitoring and metrics collection system
 */

import { auditLogger } from './audit-logger';

// Performance metrics interface
export interface PerformanceMetrics {
  timestamp: string;
  metric_type: 'api_response_time' | 'database_query' | 'page_load' | 'bundle_size';
  value: number;
  unit: 'ms' | 'bytes' | 'count';
  endpoint?: string;
  query_type?: string;
  user_id?: string;
  metadata?: Record<string, any>;
}

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  API_RESPONSE_TIME: {
    good: 200,    // < 200ms
    warning: 500, // 200-500ms
    critical: 1000 // > 1000ms
  },
  DATABASE_QUERY: {
    good: 100,    // < 100ms
    warning: 300, // 100-300ms
    critical: 1000 // > 1000ms
  },
  PAGE_LOAD: {
    good: 1000,   // < 1s
    warning: 3000, // 1-3s
    critical: 5000 // > 5s
  }
} as const;

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private timers: Map<string, number> = new Map();

  private constructor() {}

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start timing an operation
   */
  startTimer(operationId: string): void {
    this.timers.set(operationId, Date.now());
  }

  /**
   * End timing and record metric
   */
  endTimer(
    operationId: string,
    metricType: PerformanceMetrics['metric_type'],
    metadata?: Record<string, any>
  ): number {
    const startTime = this.timers.get(operationId);
    if (!startTime) {
      console.warn(`No start time found for operation: ${operationId}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(operationId);

    this.recordMetric({
      timestamp: new Date().toISOString(),
      metric_type: metricType,
      value: duration,
      unit: 'ms',
      metadata: {
        operation_id: operationId,
        ...metadata,
      },
    });

    return duration;
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);

    // Check if metric exceeds thresholds
    this.checkThresholds(metric);

    // Keep only last 1000 metrics in memory
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${metric.metric_type}: ${metric.value}${metric.unit}`, metric.metadata);
    }
  }

  /**
   * Check if metric exceeds performance thresholds
   */
  private checkThresholds(metric: PerformanceMetrics): void {
    let threshold;
    
    switch (metric.metric_type) {
      case 'api_response_time':
        threshold = PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME;
        break;
      case 'database_query':
        threshold = PERFORMANCE_THRESHOLDS.DATABASE_QUERY;
        break;
      case 'page_load':
        threshold = PERFORMANCE_THRESHOLDS.PAGE_LOAD;
        break;
      default:
        return;
    }

    let severity: 'low' | 'medium' | 'high' | 'critical' = 'low';
    let description = '';

    if (metric.value > threshold.critical) {
      severity = 'critical';
      description = `Critical performance issue: ${metric.metric_type} took ${metric.value}ms (threshold: ${threshold.critical}ms)`;
    } else if (metric.value > threshold.warning) {
      severity = 'high';
      description = `Performance warning: ${metric.metric_type} took ${metric.value}ms (threshold: ${threshold.warning}ms)`;
    }

    if (severity !== 'low') {
      // Log performance issue to audit system
      auditLogger.log({
        event_type: 'system_error',
        severity,
        action: 'performance_threshold_exceeded',
        description,
        success: false,
        metadata: {
          metric_type: metric.metric_type,
          value: metric.value,
          unit: metric.unit,
          threshold_exceeded: severity === 'critical' ? 'critical' : 'warning',
          ...metric.metadata,
        },
      });
    }
  }

  /**
   * Get performance statistics
   */
  getStatistics(timeRange: number = 3600000): { // Default 1 hour
    total: number;
    averages: Record<string, number>;
    thresholdViolations: Record<string, number>;
    slowestOperations: Array<{ operation: string; duration: number; timestamp: string }>;
  } {
    const cutoffTime = Date.now() - timeRange;
    const recentMetrics = this.metrics.filter(
      m => new Date(m.timestamp).getTime() > cutoffTime
    );

    const total = recentMetrics.length;
    const averages: Record<string, number> = {};
    const thresholdViolations: Record<string, number> = {};
    const slowestOperations: Array<{ operation: string; duration: number; timestamp: string }> = [];

    // Calculate averages by metric type
    const metricsByType = recentMetrics.reduce((acc, metric) => {
      if (!acc[metric.metric_type]) {
        acc[metric.metric_type] = [];
      }
      acc[metric.metric_type].push(metric.value);
      return acc;
    }, {} as Record<string, number[]>);

    Object.entries(metricsByType).forEach(([type, values]) => {
      averages[type] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    // Count threshold violations
    recentMetrics.forEach(metric => {
      const threshold = this.getThresholdForMetric(metric.metric_type);
      if (threshold && metric.value > threshold.warning) {
        thresholdViolations[metric.metric_type] = (thresholdViolations[metric.metric_type] || 0) + 1;
      }
    });

    // Find slowest operations
    const sortedMetrics = recentMetrics
      .filter(m => m.metadata?.operation_id)
      .sort((a, b) => b.value - a.value)
      .slice(0, 10);

    slowestOperations.push(...sortedMetrics.map(m => ({
      operation: m.metadata?.operation_id || 'unknown',
      duration: m.value,
      timestamp: m.timestamp,
    })));

    return {
      total,
      averages,
      thresholdViolations,
      slowestOperations,
    };
  }

  /**
   * Get threshold for metric type
   */
  private getThresholdForMetric(metricType: string) {
    switch (metricType) {
      case 'api_response_time':
        return PERFORMANCE_THRESHOLDS.API_RESPONSE_TIME;
      case 'database_query':
        return PERFORMANCE_THRESHOLDS.DATABASE_QUERY;
      case 'page_load':
        return PERFORMANCE_THRESHOLDS.PAGE_LOAD;
      case 'bundle_size':
        return { warning: 1024 * 1024, critical: 5 * 1024 * 1024 }; // 1MB warning, 5MB critical
      default:
        return null;
    }
  }

  /**
   * Clear metrics (for testing or memory management)
   */
  clearMetrics(): void {
    this.metrics = [];
    this.timers.clear();
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['timestamp', 'metric_type', 'value', 'unit', 'endpoint', 'metadata'];
      const rows = this.metrics.map(m => [
        m.timestamp,
        m.metric_type,
        m.value.toString(),
        m.unit,
        m.endpoint || '',
        JSON.stringify(m.metadata || {}),
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(this.metrics, null, 2);
  }
}

// Singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Utility functions for common operations
export const measureApiCall = async <T>(
  operationId: string,
  apiCall: () => Promise<T>,
  endpoint?: string
): Promise<T> => {
  performanceMonitor.startTimer(operationId);
  
  try {
    const result = await apiCall();
    performanceMonitor.endTimer(operationId, 'api_response_time', { endpoint });
    return result;
  } catch (error) {
    performanceMonitor.endTimer(operationId, 'api_response_time', { 
      endpoint, 
      error: true,
      error_message: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
};

export const measureDatabaseQuery = async <T>(
  operationId: string,
  queryCall: () => Promise<T>,
  queryType?: string
): Promise<T> => {
  performanceMonitor.startTimer(operationId);
  
  try {
    const result = await queryCall();
    performanceMonitor.endTimer(operationId, 'database_query', { query_type: queryType });
    return result;
  } catch (error) {
    performanceMonitor.endTimer(operationId, 'database_query', { 
      query_type: queryType,
      error: true,
      error_message: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
};

// Browser-side performance monitoring
export const measurePageLoad = (pageName: string): void => {
  if (typeof window !== 'undefined' && window.performance) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      performanceMonitor.recordMetric({
        timestamp: new Date().toISOString(),
        metric_type: 'page_load',
        value: loadTime,
        unit: 'ms',
        metadata: {
          page_name: pageName,
          dom_content_loaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          first_paint: navigation.responseEnd - navigation.fetchStart,
        },
      });
    }
  }
};

// Resource usage monitoring
export const measureBundleSize = (bundleName: string, sizeInBytes: number): void => {
  performanceMonitor.recordMetric({
    timestamp: new Date().toISOString(),
    metric_type: 'bundle_size',
    value: sizeInBytes,
    unit: 'bytes',
    metadata: {
      bundle_name: bundleName,
      size_mb: (sizeInBytes / (1024 * 1024)).toFixed(2),
    },
  });
};
