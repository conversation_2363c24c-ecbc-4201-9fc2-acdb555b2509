// Utility functions for fetching and managing trip template values

export interface TripTemplateValues {
  cancellation_policy: Record<string, string> | null;
  things_to_carry: string[] | null;
  inclusions: string[] | null;
  exclusions: string[] | null;
  safety_supervision: string[] | null;
  benefits: string[] | null;
  payment_terms: string | null;
  special_notes: string[] | null;
}

/**
 * Fetches template values from the most recently created trip
 * These values are commonly reused across trips
 */
export async function fetchTripTemplateValues(): Promise<TripTemplateValues> {
  try {
    const response = await fetch('/api/admin/trips/template-values');
    
    if (!response.ok) {
      console.warn('Failed to fetch template values, using defaults');
      return getDefaultTemplateValues();
    }
    
    const data = await response.json();
    return data.templateValues || getDefaultTemplateValues();
  } catch (error) {
    console.warn('Error fetching template values:', error);
    return getDefaultTemplateValues();
  }
}

/**
 * Returns default template values when database fetch fails
 */
export function getDefaultTemplateValues(): TripTemplateValues {
  return {
    cancellation_policy: {
      "30_days": "100% refund if cancelled 30+ days before trip",
      "15_days": "75% refund if cancelled 15-29 days before trip", 
      "7_days": "50% refund if cancelled 7-14 days before trip",
      "less_than_7": "No refund if cancelled less than 7 days before trip",
      "emergency": "Emergency cancellations will be reviewed case by case"
    },
    things_to_carry: [
      "Valid government-issued photo ID",
      "Comfortable walking shoes",
      "Weather-appropriate clothing",
      "Personal medications",
      "Sunscreen and sunglasses",
      "Water bottle",
      "Camera or smartphone",
      "Small backpack for day trips",
      "Cash for personal expenses",
      "Emergency contact information"
    ],
    inclusions: [
      "Accommodation as per itinerary",
      "All meals as mentioned in itinerary",
      "Transportation as per itinerary",
      "Professional tour guide",
      "All entry fees and permits",
      "First aid kit and basic medical support",
      "24/7 emergency assistance",
      "Travel insurance (basic coverage)"
    ],
    exclusions: [
      "Personal expenses and shopping",
      "Additional meals not mentioned",
      "Tips and gratuities",
      "Travel insurance (comprehensive)",
      "Medical expenses and medications",
      "Alcoholic beverages",
      "Adventure activities not included",
      "Laundry services",
      "Phone calls and internet charges",
      "Any services not mentioned in inclusions"
    ],
    safety_supervision: [
      "Experienced and certified tour guides",
      "First aid trained staff members",
      "24/7 emergency contact support",
      "Regular safety briefings",
      "Quality checked accommodation",
      "Reliable transportation with safety checks",
      "Emergency evacuation procedures",
      "Travel insurance coordination",
      "Local emergency contacts and hospitals",
      "Weather monitoring and contingency plans"
    ],
    benefits: [
      "Expert local knowledge and insights",
      "Small group sizes for personalized experience",
      "Hassle-free planning and coordination",
      "Educational and cultural immersion",
      "Safety and security throughout the journey",
      "Memorable experiences and photo opportunities",
      "Networking with like-minded travelers",
      "Professional documentation and certificates",
      "Post-trip support and memories sharing",
      "Sustainable and responsible tourism practices"
    ],
    payment_terms: "50% advance payment required at booking. Remaining 50% to be paid 15 days before departure. Payment can be made via bank transfer, UPI, or cash. Refunds as per cancellation policy.",
    special_notes: [
      "Please carry valid ID proof throughout the trip",
      "Follow guide instructions for safety",
      "Respect local customs and traditions",
      "Keep emergency contact numbers handy",
      "Inform guide of any medical conditions",
      "Weather conditions may affect itinerary",
      "Additional charges may apply for extra services",
      "Group size may vary based on bookings"
    ]
  };
}

/**
 * Merges template values with existing form data
 * Only applies template values to empty/null fields
 */
export function mergeTemplateValues(
  formData: any, 
  templateValues: TripTemplateValues
): any {
  return {
    ...formData,
    cancellation_policy: formData.cancellation_policy || templateValues.cancellation_policy,
    things_to_carry: formData.things_to_carry || templateValues.things_to_carry,
    inclusions: formData.inclusions || templateValues.inclusions,
    exclusions: formData.exclusions || templateValues.exclusions,
    safety_supervision: formData.safety_supervision || templateValues.safety_supervision,
    benefits: formData.benefits || templateValues.benefits,
    payment_terms: formData.payment_terms || templateValues.payment_terms,
    special_notes: formData.special_notes || templateValues.special_notes,
  };
}
