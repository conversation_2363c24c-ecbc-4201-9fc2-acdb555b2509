/**
 * Bundle analysis and optimization utilities
 */

// Bundle analysis types
export interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  chunks: ChunkInfo[];
  dependencies: DependencyInfo[];
  recommendations: OptimizationRecommendation[];
  timestamp: string;
}

export interface ChunkInfo {
  name: string;
  size: number;
  gzippedSize: number;
  modules: ModuleInfo[];
  isEntry: boolean;
  isAsync: boolean;
}

export interface ModuleInfo {
  name: string;
  size: number;
  reasons: string[];
  isExternal: boolean;
}

export interface DependencyInfo {
  name: string;
  version: string;
  size: number;
  usageCount: number;
  isDevDependency: boolean;
  alternatives?: string[];
}

export interface OptimizationRecommendation {
  type: 'remove_unused' | 'replace_dependency' | 'code_split' | 'lazy_load' | 'tree_shake';
  severity: 'low' | 'medium' | 'high';
  description: string;
  estimatedSavings: number;
  implementation: string;
}

// Performance budget configuration
export const PERFORMANCE_BUDGETS = {
  // Bundle size limits (in bytes)
  maxBundleSize: 500 * 1024, // 500KB
  maxChunkSize: 250 * 1024,  // 250KB
  maxInitialBundle: 200 * 1024, // 200KB
  
  // Performance metrics
  maxLoadTime: 3000, // 3 seconds
  maxFCP: 1500,      // First Contentful Paint
  maxLCP: 2500,      // Largest Contentful Paint
  maxCLS: 0.1,       // Cumulative Layout Shift
  maxFID: 100,       // First Input Delay
  
  // Resource limits
  maxImageSize: 1024 * 1024, // 1MB
  maxFontSize: 100 * 1024,   // 100KB
  maxCSSSize: 50 * 1024,     // 50KB
} as const;

// Bundle optimization utilities
export class BundleOptimizer {
  private analysis: BundleAnalysis | null = null;

  constructor(analysis?: BundleAnalysis) {
    this.analysis = analysis || null;
  }

  // Analyze bundle and generate recommendations
  generateRecommendations(): OptimizationRecommendation[] {
    if (!this.analysis) {
      return [];
    }

    const recommendations: OptimizationRecommendation[] = [];

    // Check bundle size
    if (this.analysis.totalSize > PERFORMANCE_BUDGETS.maxBundleSize) {
      recommendations.push({
        type: 'code_split',
        severity: 'high',
        description: `Bundle size (${this.formatBytes(this.analysis.totalSize)}) exceeds budget (${this.formatBytes(PERFORMANCE_BUDGETS.maxBundleSize)})`,
        estimatedSavings: this.analysis.totalSize - PERFORMANCE_BUDGETS.maxBundleSize,
        implementation: 'Implement code splitting with dynamic imports and lazy loading',
      });
    }

    // Check for large chunks
    const largeChunks = this.analysis.chunks.filter(
      chunk => chunk.size > PERFORMANCE_BUDGETS.maxChunkSize
    );

    largeChunks.forEach(chunk => {
      recommendations.push({
        type: 'code_split',
        severity: 'medium',
        description: `Chunk "${chunk.name}" (${this.formatBytes(chunk.size)}) is too large`,
        estimatedSavings: chunk.size - PERFORMANCE_BUDGETS.maxChunkSize,
        implementation: `Split "${chunk.name}" into smaller chunks using dynamic imports`,
      });
    });

    // Check for unused dependencies
    const unusedDeps = this.analysis.dependencies.filter(
      dep => dep.usageCount === 0 && !dep.isDevDependency
    );

    unusedDeps.forEach(dep => {
      recommendations.push({
        type: 'remove_unused',
        severity: 'medium',
        description: `Unused dependency: ${dep.name}`,
        estimatedSavings: dep.size,
        implementation: `Remove "${dep.name}" from package.json and run npm install`,
      });
    });

    // Check for large dependencies with alternatives
    const largeDeps = this.analysis.dependencies.filter(
      dep => dep.size > 100 * 1024 && dep.alternatives && dep.alternatives.length > 0
    );

    largeDeps.forEach(dep => {
      recommendations.push({
        type: 'replace_dependency',
        severity: 'medium',
        description: `Large dependency: ${dep.name} (${this.formatBytes(dep.size)})`,
        estimatedSavings: dep.size * 0.5, // Estimate 50% savings
        implementation: `Consider replacing with: ${dep.alternatives?.join(', ') || 'lighter alternatives'}`,
      });
    });

    // Check for opportunities to tree shake
    const treeShakeOpportunities = this.analysis.chunks.flatMap(chunk =>
      chunk.modules.filter(module => 
        module.name.includes('node_modules') && 
        module.size > 10 * 1024 &&
        module.reasons.length === 1
      )
    );

    if (treeShakeOpportunities.length > 0) {
      const totalSavings = treeShakeOpportunities.reduce((sum, module) => sum + module.size, 0);
      recommendations.push({
        type: 'tree_shake',
        severity: 'low',
        description: `${treeShakeOpportunities.length} modules could be tree-shaken`,
        estimatedSavings: totalSavings * 0.3, // Estimate 30% savings
        implementation: 'Use ES6 imports and ensure sideEffects: false in package.json',
      });
    }

    return recommendations.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  // Generate performance budget report
  generateBudgetReport(): {
    status: 'pass' | 'warn' | 'fail';
    violations: Array<{
      metric: string;
      current: number;
      budget: number;
      severity: 'warn' | 'fail';
    }>;
  } {
    if (!this.analysis) {
      return { status: 'fail', violations: [] };
    }

    const violations: Array<{
      metric: string;
      current: number;
      budget: number;
      severity: 'warn' | 'fail';
    }> = [];

    // Check bundle size
    if (this.analysis.totalSize > PERFORMANCE_BUDGETS.maxBundleSize) {
      violations.push({
        metric: 'Bundle Size',
        current: this.analysis.totalSize,
        budget: PERFORMANCE_BUDGETS.maxBundleSize,
        severity: 'fail' as const,
      });
    }

    // Check chunk sizes
    const largeChunks = this.analysis.chunks.filter(
      chunk => chunk.size > PERFORMANCE_BUDGETS.maxChunkSize
    );

    largeChunks.forEach(chunk => {
      violations.push({
        metric: `Chunk Size (${chunk.name})`,
        current: chunk.size,
        budget: PERFORMANCE_BUDGETS.maxChunkSize,
        severity: 'warn' as const,
      });
    });

    // Determine overall status
    const hasFailures = violations.some(v => v.severity === 'fail');
    const hasWarnings = violations.some(v => v.severity === 'warn');

    const status = hasFailures ? 'fail' : hasWarnings ? 'warn' : 'pass';

    return { status, violations };
  }

  // Format bytes for human reading
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Mock bundle analysis for demonstration
export function createMockBundleAnalysis(): BundleAnalysis {
  return {
    totalSize: 450 * 1024, // 450KB
    gzippedSize: 150 * 1024, // 150KB
    chunks: [
      {
        name: 'main',
        size: 200 * 1024,
        gzippedSize: 70 * 1024,
        isEntry: true,
        isAsync: false,
        modules: [
          {
            name: 'react',
            size: 50 * 1024,
            reasons: ['main.tsx'],
            isExternal: true,
          },
          {
            name: 'next',
            size: 80 * 1024,
            reasons: ['main.tsx'],
            isExternal: true,
          },
        ],
      },
      {
        name: 'admin',
        size: 150 * 1024,
        gzippedSize: 50 * 1024,
        isEntry: false,
        isAsync: true,
        modules: [
          {
            name: '@tanstack/react-query',
            size: 40 * 1024,
            reasons: ['admin/page.tsx'],
            isExternal: true,
          },
        ],
      },
    ],
    dependencies: [
      {
        name: 'react',
        version: '18.2.0',
        size: 50 * 1024,
        usageCount: 15,
        isDevDependency: false,
      },
      {
        name: 'lodash',
        version: '4.17.21',
        size: 70 * 1024,
        usageCount: 0, // Unused
        isDevDependency: false,
        alternatives: ['lodash-es', 'ramda'],
      },
    ],
    recommendations: [],
    timestamp: new Date().toISOString(),
  };
}

// Performance monitoring for client-side
export function measureWebVitals() {
  if (typeof window === 'undefined') return;

  // Measure Core Web Vitals
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      const perfEntry = entry as PerformanceEntry & { value?: number };
      const value = perfEntry.value || entry.duration || 0;

      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`${entry.name}: ${value}`);
      }

      // Send to monitoring service
      if (entry.name === 'LCP' && value > PERFORMANCE_BUDGETS.maxLCP) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`LCP (${value}ms) exceeds budget (${PERFORMANCE_BUDGETS.maxLCP}ms)`);
        }
      }

      if (entry.name === 'FID' && value > PERFORMANCE_BUDGETS.maxFID) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`FID (${value}ms) exceeds budget (${PERFORMANCE_BUDGETS.maxFID}ms)`);
        }
      }

      if (entry.name === 'CLS' && value > PERFORMANCE_BUDGETS.maxCLS) {
        if (process.env.NODE_ENV === 'development') {
          console.warn(`CLS (${value}) exceeds budget (${PERFORMANCE_BUDGETS.maxCLS})`);
        }
      }
    });
  });

  // Observe Core Web Vitals
  observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
}

// Bundle analysis CLI utility
export function analyzeBundleFromStats(_statsPath: string): BundleAnalysis {
  // This would read webpack stats.json in a real implementation
  // For now, return mock data
  return createMockBundleAnalysis();
}
