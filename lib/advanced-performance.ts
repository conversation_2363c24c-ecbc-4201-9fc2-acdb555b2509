/**
 * Advanced Performance Optimization System
 * 
 * This module provides advanced performance optimization techniques including
 * intelligent preloading, resource prioritization, and performance monitoring.
 */

import { performanceMonitor } from './performance-monitor';
import { cacheInstances } from './cache-manager';

// Performance API type extensions
interface PerformanceEventTiming extends PerformanceEntry {
  processingStart: number;
  processingEnd: number;
  cancelable: boolean;
}

// Performance optimization configuration
export const performanceConfig = {
  // Preloading thresholds
  preloadThreshold: 0.8, // Start preloading when 80% of viewport is reached
  intersectionThreshold: 0.1, // Trigger when 10% of element is visible
  
  // Resource priorities
  criticalResourceTimeout: 3000, // 3 seconds for critical resources
  nonCriticalResourceTimeout: 10000, // 10 seconds for non-critical resources
  
  // Performance budgets
  budgets: {
    firstContentfulPaint: 1500, // 1.5 seconds
    largestContentfulPaint: 2500, // 2.5 seconds
    firstInputDelay: 100, // 100ms
    cumulativeLayoutShift: 0.1, // 0.1 CLS score
    totalBlockingTime: 300, // 300ms
  },
  
  // Memory management
  maxCacheSize: 50 * 1024 * 1024, // 50MB
  gcThreshold: 0.8, // Trigger GC when 80% of memory is used
} as const;

// Resource priority levels
export type ResourcePriority = 'critical' | 'high' | 'medium' | 'low';

// Performance metrics interface
export interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  tbt?: number; // Total Blocking Time
  ttfb?: number; // Time to First Byte
}

// Intelligent preloader class
export class IntelligentPreloader {
  private observer: IntersectionObserver | null = null;
  private preloadedResources = new Set<string>();
  private preloadQueue: Array<{ url: string; priority: ResourcePriority }> = [];
  private isProcessing = false;

  constructor() {
    this.initializeObserver();
  }

  private initializeObserver() {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return;
    }

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            const preloadUrl = element.dataset.preload;
            const priority = (element.dataset.priority as ResourcePriority) || 'medium';
            
            if (preloadUrl && !this.preloadedResources.has(preloadUrl)) {
              this.queuePreload(preloadUrl, priority);
            }
          }
        });
      },
      {
        threshold: performanceConfig.intersectionThreshold,
        rootMargin: '50px',
      }
    );
  }

  public observeElement(element: HTMLElement) {
    if (this.observer) {
      this.observer.observe(element);
    }
  }

  public unobserveElement(element: HTMLElement) {
    if (this.observer) {
      this.observer.unobserve(element);
    }
  }

  private queuePreload(url: string, priority: ResourcePriority) {
    this.preloadQueue.push({ url, priority });
    this.preloadQueue.sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));
    
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  private getPriorityWeight(priority: ResourcePriority): number {
    const weights = { critical: 4, high: 3, medium: 2, low: 1 };
    return weights[priority];
  }

  private async processQueue() {
    if (this.preloadQueue.length === 0) {
      this.isProcessing = false;
      return;
    }

    this.isProcessing = true;
    const { url, priority } = this.preloadQueue.shift()!;

    try {
      await this.preloadResource(url, priority);
      this.preloadedResources.add(url);
    } catch (error) {
      console.warn(`Failed to preload resource: ${url}`, error);
    }

    // Continue processing queue
    setTimeout(() => this.processQueue(), 50);
  }

  private async preloadResource(url: string, priority: ResourcePriority): Promise<void> {
    const timeout = priority === 'critical' 
      ? performanceConfig.criticalResourceTimeout 
      : performanceConfig.nonCriticalResourceTimeout;

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Preload timeout for ${url}`));
      }, timeout);

      if (url.match(/\.(jpg|jpeg|png|webp|avif)$/i)) {
        // Preload image
        const img = new Image();
        img.onload = () => {
          clearTimeout(timeoutId);
          resolve();
        };
        img.onerror = () => {
          clearTimeout(timeoutId);
          reject(new Error(`Failed to load image: ${url}`));
        };
        img.src = url;
      } else {
        // Preload other resources
        fetch(url, { 
          method: 'HEAD',
          priority: priority === 'critical' ? 'high' : 'low' 
        } as RequestInit)
          .then(() => {
            clearTimeout(timeoutId);
            resolve();
          })
          .catch((error) => {
            clearTimeout(timeoutId);
            reject(error);
          });
      }
    });
  }

  public destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.preloadQueue = [];
    this.preloadedResources.clear();
  }
}

// Performance monitor with advanced metrics
export class AdvancedPerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    // Observe paint metrics
    try {
      const paintObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.fcp = entry.startTime;
            this.checkBudget('fcp', entry.startTime);
          }
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);
    } catch (error) {
      console.warn('Paint observer not supported:', error);
    }

    // Observe LCP
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.lcp = lastEntry.startTime;
        this.checkBudget('lcp', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);
    } catch (error) {
      console.warn('LCP observer not supported:', error);
    }

    // Observe FID
    try {
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          const eventEntry = entry as PerformanceEventTiming;
          if (eventEntry.processingStart) {
            this.metrics.fid = eventEntry.processingStart - eventEntry.startTime;
            this.checkBudget('fid', this.metrics.fid);
          }
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);
    } catch (error) {
      console.warn('FID observer not supported:', error);
    }

    // Observe CLS
    try {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
            this.metrics.cls = clsValue;
            this.checkBudget('cls', clsValue);
          }
        });
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);
    } catch (error) {
      console.warn('CLS observer not supported:', error);
    }
  }

  private checkBudget(metric: keyof PerformanceMetrics, value: number) {
    const budgets = performanceConfig.budgets;
    let budget: number;
    let unit: string;

    switch (metric) {
      case 'fcp':
        budget = budgets.firstContentfulPaint;
        unit = 'ms';
        break;
      case 'lcp':
        budget = budgets.largestContentfulPaint;
        unit = 'ms';
        break;
      case 'fid':
        budget = budgets.firstInputDelay;
        unit = 'ms';
        break;
      case 'cls':
        budget = budgets.cumulativeLayoutShift;
        unit = '';
        break;
      default:
        return;
    }

    if (value > budget) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Performance budget exceeded for ${metric}: ${value}${unit} > ${budget}${unit}`);
      }

      // Report to performance monitoring
      performanceMonitor.recordMetric({
        timestamp: new Date().toISOString(),
        metric_type: 'page_load',
        value,
        unit: unit as any,
        metadata: {
          metric,
          budget,
          exceeded: true,
          url: window.location.href,
        },
      });
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Memory management utilities
export class MemoryManager {
  private memoryUsage = 0;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startMonitoring();
  }

  private startMonitoring() {
    if (typeof window === 'undefined') return;

    this.checkInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, 30000); // Check every 30 seconds
  }

  private checkMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.memoryUsage = memory.usedJSHeapSize;
      
      const usageRatio = this.memoryUsage / performanceConfig.maxCacheSize;
      
      if (usageRatio > performanceConfig.gcThreshold) {
        this.triggerGarbageCollection();
      }
    }
  }

  private triggerGarbageCollection() {
    console.log('Triggering garbage collection due to high memory usage');
    
    // Clear least recently used cache entries
    Object.values(cacheInstances).forEach(cache => {
      cache.cleanup();
    });

    // Force garbage collection if available (Chrome DevTools)
    if ('gc' in window) {
      (window as any).gc();
    }
  }

  public getMemoryUsage(): number {
    return this.memoryUsage;
  }

  public destroy() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }
}

// Global instances
export const intelligentPreloader = new IntelligentPreloader();
export const advancedPerformanceMonitor = new AdvancedPerformanceMonitor();
export const memoryManager = new MemoryManager();

// Utility functions
export function measureComponentRender<T extends (...args: any[]) => any>(
  componentName: string,
  renderFunction: T
): T {
  return ((...args: Parameters<T>) => {
    const startTime = performance.now();
    const result = renderFunction(...args);
    const endTime = performance.now();
    
    performanceMonitor.recordMetric({
      timestamp: new Date().toISOString(),
      metric_type: 'page_load',
      value: endTime - startTime,
      unit: 'ms',
      metadata: {
        component: componentName,
        type: 'render',
      },
    });
    
    return result;
  }) as T;
}

export function optimizeImageLoading(
  src: string,
  options: {
    priority?: ResourcePriority;
    lazy?: boolean;
    preload?: boolean;
  } = {}
): string {
  const { priority = 'medium', lazy = true, preload = false } = options;
  
  if (preload && !intelligentPreloader['preloadedResources'].has(src)) {
    intelligentPreloader['queuePreload'](src, priority);
  }
  
  return src;
}

// Cleanup function
export function cleanupPerformanceOptimizations() {
  intelligentPreloader.destroy();
  advancedPerformanceMonitor.destroy();
  memoryManager.destroy();
}
