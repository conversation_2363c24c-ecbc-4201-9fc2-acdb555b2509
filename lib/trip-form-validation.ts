'use client';

import { TripFormData } from '@/types/trip';

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  missingRequiredFields: string[];
}

// Define required fields based on database NOT NULL constraints
export const REQUIRED_FIELDS = {
  title: 'Trip Title',
  destination: 'Destination',
  days: 'Days',
  nights: 'Nights',
  price_per_person: 'Price per Person',
  difficulty: 'Difficulty Level'
};

// Define field validation rules
export const FIELD_VALIDATIONS = {
  title: {
    required: true,
    minLength: 5,
    maxLength: 200,
    message: 'Title must be between 5 and 200 characters'
  },
  description: {
    required: false, // Description is nullable in DB but recommended
    minLength: 10,
    maxLength: 500,
    message: 'Description must be between 10 and 500 characters'
  },
  destination: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: 'Destination must be between 2 and 100 characters'
  },
  days: {
    required: true,
    min: 1,
    max: 30,
    message: 'Days must be between 1 and 30'
  },
  nights: {
    required: true,
    min: 0,
    max: 31,
    message: 'Nights must be between 0 and 31',
    autoCalculated: true // Special flag to indicate this field is auto-calculated
  },
  price_per_person: {
    required: true,
    min: 0, // Allow 0 for free trips
    max: 1000000,
    message: 'Price must be between 0 and 10,00,000'
  },
  difficulty: {
    required: true,
    options: ['easy', 'moderate', 'challenging', 'difficult'],
    message: 'Please select a difficulty level'
  },
  detailed_description: {
    required: false,
    minLength: 10,
    maxLength: 5000,
    message: 'Detailed description must be between 10 and 5000 characters'
  },
  min_age: {
    required: false,
    min: 1,
    max: 100,
    message: 'Minimum age must be between 1 and 100'
  },
  max_age: {
    required: false,
    min: 1,
    max: 100,
    message: 'Maximum age must be between 1 and 100'
  }
};

/**
 * Validate a single field
 */
export function validateField(field: keyof TripFormData, value: any): string | null {
  const validation = FIELD_VALIDATIONS[field as keyof typeof FIELD_VALIDATIONS];
  if (!validation) return null;

  // Check if required field is empty
  if (validation.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return `${REQUIRED_FIELDS[field as keyof typeof REQUIRED_FIELDS] || field} is required`;
  }

  // Skip further validation if field is not required and empty
  if (!validation.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return null;
  }

  // String validations
  if (typeof value === 'string') {
    const stringValidation = validation as any;
    if (stringValidation.minLength && value.length < stringValidation.minLength) {
      return validation.message;
    }
    if (stringValidation.maxLength && value.length > stringValidation.maxLength) {
      return validation.message;
    }
  }

  // Number validations
  if (typeof value === 'number') {
    const numberValidation = validation as any;
    if (numberValidation.min !== undefined && value < numberValidation.min) {
      return validation.message;
    }
    if (numberValidation.max !== undefined && value > numberValidation.max) {
      return validation.message;
    }
  }

  // Options validation
  const optionsValidation = validation as any;
  if (optionsValidation.options && !optionsValidation.options.includes(value)) {
    return validation.message;
  }

  return null;
}

/**
 * Validate the entire form
 */
export function validateTripForm(formData: Partial<TripFormData>): ValidationResult {
  const errors: ValidationError[] = [];
  const missingRequiredFields: string[] = [];

  // Check all fields
  Object.keys(FIELD_VALIDATIONS).forEach(field => {
    const fieldKey = field as keyof TripFormData;
    const value = formData[fieldKey];
    const error = validateField(fieldKey, value);
    
    if (error) {
      errors.push({ field, message: error });
      
      // Check if it's a required field that's missing
      const validation = FIELD_VALIDATIONS[field as keyof typeof FIELD_VALIDATIONS];
      if (validation.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        missingRequiredFields.push(REQUIRED_FIELDS[field as keyof typeof REQUIRED_FIELDS] || field);
      }
    }
  });

  // Custom validations
  
  // Nights should equal days - 1
  if (formData.days && formData.nights !== undefined) {
    const expectedNights = Math.max(0, formData.days - 1);
    if (formData.nights !== expectedNights) {
      errors.push({
        field: 'nights',
        message: 'Nights must be exactly one less than days (days - 1)'
      });
    }
  }

  // Max age should be >= min age
  if (formData.min_age && formData.max_age && formData.min_age > formData.max_age) {
    errors.push({
      field: 'max_age',
      message: 'Maximum age must be greater than or equal to minimum age'
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    missingRequiredFields
  };
}

/**
 * Get field error message
 */
export function getFieldError(errors: ValidationError[], field: string): string | null {
  const error = errors.find(e => e.field === field);
  return error ? error.message : null;
}

/**
 * Check if field has error
 */
export function hasFieldError(errors: ValidationError[], field: string): boolean {
  return errors.some(e => e.field === field);
}

/**
 * Get required field indicator
 */
export function isRequiredField(field: keyof TripFormData): boolean {
  const validation = FIELD_VALIDATIONS[field as keyof typeof FIELD_VALIDATIONS];
  return validation?.required || false;
}

/**
 * Format validation errors for toast display
 */
export function formatValidationErrors(errors: ValidationError[]): string {
  if (errors.length === 0) return '';
  
  if (errors.length === 1) {
    return errors[0].message;
  }
  
  return `Please fix the following errors:\n${errors.map(e => `• ${e.message}`).join('\n')}`;
}

/**
 * Format missing required fields for toast display
 */
export function formatMissingRequiredFields(missingFields: string[]): string {
  if (missingFields.length === 0) return '';
  
  if (missingFields.length === 1) {
    return `Please fill in the required field: ${missingFields[0]}`;
  }
  
  return `Please fill in the following required fields:\n${missingFields.map(f => `• ${f}`).join('\n')}`;
}


