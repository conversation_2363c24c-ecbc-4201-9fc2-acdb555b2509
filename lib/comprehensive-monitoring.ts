/**
 * Comprehensive Monitoring and Analytics System
 * 
 * This module provides a complete monitoring solution including user analytics,
 * performance tracking, error monitoring, and business metrics.
 */

import { performanceMonitor } from './performance-monitor';
import { auditLogger } from './audit-logger';

// Analytics event types
export type AnalyticsEventType = 
  | 'page_view'
  | 'user_interaction'
  | 'conversion'
  | 'error'
  | 'performance'
  | 'business_metric';

// User interaction types
export type InteractionType = 
  | 'click'
  | 'scroll'
  | 'form_submit'
  | 'search'
  | 'filter'
  | 'navigation'
  | 'download'
  | 'share';

// Conversion types
export type ConversionType = 
  | 'inquiry_submitted'
  | 'trip_viewed'
  | 'contact_form'

  | 'phone_call'
  | 'email_click';

// Analytics event interface
export interface AnalyticsEvent {
  type: AnalyticsEventType;
  name: string;
  timestamp: string;
  sessionId: string;
  userId?: string;
  properties: Record<string, any>;
  metadata?: Record<string, any>;
}

// User session interface
export interface UserSession {
  id: string;
  startTime: string;
  endTime?: string;
  pageViews: number;
  interactions: number;
  conversions: number;
  userAgent: string;
  referrer: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

// Business metrics interface
export interface BusinessMetrics {
  totalInquiries: number;
  conversionRate: number;
  averageSessionDuration: number;
  bounceRate: number;
  topPages: Array<{ page: string; views: number }>;
  topTrips: Array<{ trip: string; views: number }>;
  trafficSources: Array<{ source: string; visitors: number }>;
}

// Comprehensive monitoring class
export class ComprehensiveMonitor {
  private sessionId: string;
  private session: UserSession;
  private events: AnalyticsEvent[] = [];
  private isInitialized = false;
  private flushInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.session = this.initializeSession();
    this.initialize();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private initializeSession(): UserSession {
    const urlParams = new URLSearchParams(window.location.search);
    
    return {
      id: this.sessionId,
      startTime: new Date().toISOString(),
      pageViews: 0,
      interactions: 0,
      conversions: 0,
      userAgent: navigator.userAgent,
      referrer: document.referrer,
      utmSource: urlParams.get('utm_source') || undefined,
      utmMedium: urlParams.get('utm_medium') || undefined,
      utmCampaign: urlParams.get('utm_campaign') || undefined,
    };
  }

  private initialize() {
    if (typeof window === 'undefined' || this.isInitialized) return;

    // Track page visibility changes
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    
    // Track page unload
    window.addEventListener('beforeunload', this.handlePageUnload);
    
    // Track scroll depth
    this.initializeScrollTracking();
    
    // Track clicks
    this.initializeClickTracking();
    
    // Start periodic flush
    this.flushInterval = setInterval(() => {
      this.flushEvents();
    }, 30000); // Flush every 30 seconds

    this.isInitialized = true;
  }

  private handleVisibilityChange = () => {
    if (document.hidden) {
      this.trackEvent({
        type: 'user_interaction',
        name: 'page_hidden',
        properties: {
          duration: Date.now() - new Date(this.session.startTime).getTime(),
        },
      });
    } else {
      this.trackEvent({
        type: 'user_interaction',
        name: 'page_visible',
        properties: {},
      });
    }
  };

  private handlePageUnload = () => {
    this.session.endTime = new Date().toISOString();
    this.flushEvents(true); // Force immediate flush
  };

  private initializeScrollTracking() {
    let maxScrollDepth = 0;
    let scrollTimeout: NodeJS.Timeout;
    const milestones = new Set<number>(); // Track which milestones have been reached

    const trackScroll = () => {
      const scrollDepth = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );

      if (scrollDepth > maxScrollDepth) {
        const previousMax = maxScrollDepth;
        maxScrollDepth = scrollDepth;

        // Track milestone scroll depths
        [25, 50, 75, 100].forEach(milestone => {
          if (scrollDepth >= milestone && !milestones.has(milestone)) {
            milestones.add(milestone);
            this.trackInteraction('scroll', { depth: milestone });
          }
        });
      }
    };

    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(trackScroll, 100);
    });
  }

  private initializeClickTracking() {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const tagName = target.tagName.toLowerCase();
      const className = target.className;
      const id = target.id;
      const href = target.getAttribute('href');
      const text = target.textContent?.trim().substring(0, 100);

      this.trackInteraction('click', {
        element: tagName,
        className,
        id,
        href,
        text,
        x: event.clientX,
        y: event.clientY,
      });
    });
  }

  // Public methods
  public trackPageView(page: string, title?: string) {
    this.session.pageViews++;
    
    this.trackEvent({
      type: 'page_view',
      name: 'page_viewed',
      properties: {
        page,
        title: title || document.title,
        url: window.location.href,
        referrer: document.referrer,
      },
    });
  }

  public trackInteraction(type: InteractionType, properties: Record<string, any> = {}) {
    this.session.interactions++;
    
    this.trackEvent({
      type: 'user_interaction',
      name: type,
      properties: {
        ...properties,
        page: window.location.pathname,
      },
    });
  }

  public trackConversion(type: ConversionType, properties: Record<string, any> = {}) {
    this.session.conversions++;
    
    this.trackEvent({
      type: 'conversion',
      name: type,
      properties: {
        ...properties,
        page: window.location.pathname,
        sessionDuration: Date.now() - new Date(this.session.startTime).getTime(),
      },
    });
  }

  public trackError(error: Error, context?: string) {
    this.trackEvent({
      type: 'error',
      name: 'client_error',
      properties: {
        message: error.message,
        stack: error.stack,
        context,
        page: window.location.pathname,
      },
    });
  }

  public trackPerformance(metric: string, value: number, unit: string) {
    this.trackEvent({
      type: 'performance',
      name: metric,
      properties: {
        value,
        unit,
        page: window.location.pathname,
      },
    });
  }

  public trackBusinessMetric(metric: string, value: number, properties: Record<string, any> = {}) {
    this.trackEvent({
      type: 'business_metric',
      name: metric,
      properties: {
        value,
        ...properties,
      },
    });
  }

  private trackEvent(event: Omit<AnalyticsEvent, 'timestamp' | 'sessionId'>) {
    const fullEvent: AnalyticsEvent = {
      ...event,
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
    };

    this.events.push(fullEvent);

    // Flush immediately for critical events
    if (event.type === 'conversion' || event.type === 'error') {
      this.flushEvents();
    }
  }

  private async flushEvents(force = false) {
    if (this.events.length === 0) return;

    const eventsToFlush = [...this.events];
    this.events = [];

    try {
      await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session: this.session,
          events: eventsToFlush,
        }),
        keepalive: force, // Use keepalive for page unload
      });
    } catch (error) {
      console.error('Failed to flush analytics events:', error);
      // Re-add events to queue if flush failed (unless forced)
      if (!force) {
        this.events.unshift(...eventsToFlush);
      }
    }
  }

  public destroy() {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }

    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('beforeunload', this.handlePageUnload);

    this.flushEvents(true);
    this.isInitialized = false;
  }
}

// Real-time dashboard data provider
export class DashboardDataProvider {
  private updateInterval: NodeJS.Timeout | null = null;
  private subscribers: Array<(data: BusinessMetrics) => void> = [];

  public subscribe(callback: (data: BusinessMetrics) => void) {
    this.subscribers.push(callback);
    
    if (this.subscribers.length === 1) {
      this.startUpdates();
    }
  }

  public unsubscribe(callback: (data: BusinessMetrics) => void) {
    this.subscribers = this.subscribers.filter(sub => sub !== callback);
    
    if (this.subscribers.length === 0) {
      this.stopUpdates();
    }
  }

  private startUpdates() {
    this.updateInterval = setInterval(async () => {
      try {
        const data = await this.fetchDashboardData();
        this.subscribers.forEach(callback => callback(data));
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      }
    }, 60000); // Update every minute
  }

  private stopUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  private async fetchDashboardData(): Promise<BusinessMetrics> {
    const response = await fetch('/api/analytics/dashboard');
    if (!response.ok) {
      throw new Error('Failed to fetch dashboard data');
    }
    return response.json();
  }
}

// Global instances
export const comprehensiveMonitor = typeof window !== 'undefined' ? new ComprehensiveMonitor() : null;
export const dashboardDataProvider = new DashboardDataProvider();

// Utility functions
export function trackTripView(tripId: string, tripTitle: string) {
  comprehensiveMonitor?.trackInteraction('navigation', {
    type: 'trip_view',
    tripId,
    tripTitle,
  });
}

export function trackInquirySubmission(tripId?: string, source?: string) {
  comprehensiveMonitor?.trackConversion('inquiry_submitted', {
    tripId,
    source,
  });
}

export function trackSearchQuery(query: string, results: number) {
  comprehensiveMonitor?.trackInteraction('search', {
    query,
    results,
  });
}

export function trackFilterUsage(filterType: string, filterValue: string) {
  comprehensiveMonitor?.trackInteraction('filter', {
    filterType,
    filterValue,
  });
}

// Cleanup function
export function cleanupMonitoring() {
  comprehensiveMonitor?.destroy();
}
