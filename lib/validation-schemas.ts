import { z } from 'zod';

// Enhanced validation patterns
const phoneRegex = /^[0-9]{10}$/; // Exactly 10 digits, no symbols


// Contact form validation
export const contactFormSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  
  phone: z.string()
    .regex(phoneRegex, 'Phone number must be exactly 10 digits with no symbols')
    .length(10, 'Phone number must be exactly 10 digits'),
  
  subject: z.string()
    .min(5, 'Subject must be at least 5 characters')
    .max(200, 'Subject must be less than 200 characters'),
  
  message: z.string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message must be less than 2000 characters'),
  
  csrfToken: z.string().optional(),
});

// Trip inquiry validation
export const tripInquirySchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  
  phone: z.string()
    .regex(phoneRegex, 'Phone number must be exactly 10 digits with no symbols')
    .length(10, 'Phone number must be exactly 10 digits'),
  
  tripId: z.string()
    .uuid('Invalid trip ID'),
  
  numberOfPeople: z.number()
    .int('Number of people must be a whole number')
    .min(1, 'At least 1 person required')
    .max(100, 'Maximum 100 people allowed'),
  
  preferredDate: z.string()
    .datetime('Invalid date format')
    .optional(),
  
  message: z.string()
    .max(1000, 'Message must be less than 1000 characters')
    .optional(),
  
  csrfToken: z.string().optional(),
});

// Admin login validation
export const adminLoginSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters'),
  
  csrfToken: z.string().optional(),
});

// Trip creation/update validation
export const tripSchema = z.object({
  title: z.string()
    .min(5, 'Title must be at least 5 characters')
    .max(200, 'Title must be less than 200 characters'),

  slug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(100, 'Slug must be less than 100 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),

  description: z.string()
    .min(50, 'Description must be at least 50 characters')
    .max(500, 'Description must be less than 500 characters'),

  detailed_description: z.string()
    .min(100, 'Detailed description must be at least 100 characters')
    .max(5000, 'Detailed description must be less than 5000 characters'),

  destination: z.string()
    .min(2, 'Destination must be at least 2 characters')
    .max(100, 'Destination must be less than 100 characters'),

  days: z.number()
    .int('Days must be a whole number')
    .min(1, 'Trip must be at least 1 day')
    .max(30, 'Trip cannot exceed 30 days'),

  nights: z.number()
    .int('Nights must be a whole number')
    .min(0, 'Nights cannot be negative')
    .max(31, 'Nights cannot exceed 31'),

  price_per_person: z.number()
    .min(0, 'Price cannot be negative')
    .max(1000000, 'Price cannot exceed 10,00,000'),

  difficulty: z.enum(['Easy', 'Moderate', 'Challenging', 'Difficult']),

  is_trek: z.boolean(),
  is_published: z.boolean(),

  featured_image_url: z.string()
    .url('Please enter a valid URL')
    .optional(),

  itinerary: z.record(z.any()).optional(),
  inclusions: z.array(z.string()).optional(),
  exclusions: z.array(z.string()).optional(),
}).refine((data) => {
  // Ensure nights equals days - 1
  const expectedNights = Math.max(0, data.days - 1);
  return data.nights === expectedNights;
}, {
  message: 'Nights must be exactly one less than days (days - 1)',
  path: ['nights']
});

// Blog post validation
export const blogPostSchema = z.object({
  title: z.string()
    .min(5, 'Title must be at least 5 characters')
    .max(200, 'Title must be less than 200 characters'),
  
  slug: z.string()
    .min(3, 'Slug must be at least 3 characters')
    .max(100, 'Slug must be less than 100 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  
  content: z.string()
    .min(100, 'Content must be at least 100 characters')
    .max(50000, 'Content must be less than 50,000 characters'),
  
  excerpt: z.string()
    .min(50, 'Excerpt must be at least 50 characters')
    .max(500, 'Excerpt must be less than 500 characters')
    .optional(),
  
  category: z.string()
    .min(2, 'Category must be at least 2 characters')
    .max(50, 'Category must be less than 50 characters')
    .optional(),
  
  tags: z.array(z.string()).optional(),
  
  featured_image_url: z.string()
    .url('Please enter a valid URL')
    .optional(),
  
  is_published: z.boolean(),
});

// Password validation for trip photos
export const tripPhotoPasswordSchema = z.object({
  albumId: z.string()
    .uuid('Invalid album ID'),
  
  password: z.string()
    .min(1, 'Password is required')
    .max(100, 'Password must be less than 100 characters'),
});

// User registration validation
export const userRegistrationSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters'),
  
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  full_name: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters'),
  
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be less than 50 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
});

// Export types for TypeScript
export type ContactFormData = z.infer<typeof contactFormSchema>;
export type TripInquiryData = z.infer<typeof tripInquirySchema>;
export type AdminLoginData = z.infer<typeof adminLoginSchema>;
export type TripData = z.infer<typeof tripSchema>;
export type BlogPostData = z.infer<typeof blogPostSchema>;
export type TripPhotoPasswordData = z.infer<typeof tripPhotoPasswordSchema>;
export type UserRegistrationData = z.infer<typeof userRegistrationSchema>;

// Validation helper function
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return { success: false, errors };
    }
    return { success: false, errors: ['Validation failed'] };
  }
}
